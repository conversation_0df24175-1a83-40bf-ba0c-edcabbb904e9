Here is a **structured breakdown** of the requested improvements and considerations, organized into key areas for implementation and strategy:

---

## 🔧 1. Improve Error Handling in the Data Retriever

### Objectives:

* Make the agent aware of specific failures (e.g., no connection, no data) so it can give meaningful responses.

### Implementation Ideas:

* **Categorize errors** with clear codes or types:

  * `DATA_RETRIEVER_ERROR_CONNECTION`
  * `DATA_RETRIEVER_ERROR_NO_DATA`
  * `DATA_RETRIEVER_ERROR_TIMEOUT`
  * `DATA_RETRIEVER_ERROR_INVALID_PARAMS`
* **Standardize return format**:

  ```json
  {
    "success": false,
    "error_code": "DATA_RETRIEVER_ERROR_NO_DATA",
    "message": "No data available for the selected time range."
  }
  ```
* In the agent logic, map each error to a specific fallback response (natural language):

  * *“Je ne trouve aucune donnée pour la période demandée. Pouvez-vous vérifier les filtres ou la période sélectionnée ?”*

---

## 📉 2. Move Plotting Logic to a Separate Tool Agent

### Objectives:

* Simplify the main agent logic.
* Improve plotting quality and separation of concerns.

### Strategy:

* Introduce a dedicated `plot_generator` tool/agent.
* Main agent generates **plot requests** with context like:

  ```json
  {
    "type": "timeseries",
    "data": [...],
    "instructions": "Highlight weekends and display average line."
  }
  ```
* Plotting agent returns a Plotly object or saved image URL, with caption if needed.

### Benefits:

* Easier to enhance visuals.
* Main agent can focus on reasoning and summarizing insights.
* Enables instructions like:

  * *“Ajoute une ligne de tendance”*
  * *“Affiche les variations par site sur la même figure”*

---

## 🧠 3. Define Agent Behavior & Reasoning Logic

### 3.1 What Info Should Be Collected Before Querying Data

| Info Type       | Required | Default/Prompted Behavior                            |
| --------------- | -------- | ---------------------------------------------------- |
| Timeframe       | ✅ Yes    | Default to "last month" if missing                   |
| Fluid Type      | ✅ Yes    | Ask: “Quel type d’énergie souhaitez-vous analyser ?” |
| Site / Location | ⚠️ Maybe | Prompt if multiple sites are available               |
| Granularity     | ⚠️ Maybe | Default to daily unless question implies otherwise   |
| Indicator Type  | ✅ Yes    | Infer from question or ask explicitly                |

### 3.2 Handling Ambiguous Questions (Multiple Algos/Labels)

#### Example:

*“Détecte les anomalies de consommation.”*

Should ask:

* “Souhaitez-vous une détection basée sur des seuils statistiques, l’historique ou une méthode machine learning ?”

#### Implementation:

* If multiple algorithms/labels apply:

  * Provide a **short summary of each option**
  * Ask the user to select, or use the most interpretable one by default (e.g., IQR for anomalies).

---

## 🔎 4. Understand Product Usage by Product Owner

### Purpose:

* Build better system prompts or internal logic.
* Anticipate typical questions and preferred formats.

### Approach:

* Shadow or interview the product owner:

  * How do they frame questions?
  * What outputs do they expect (KPIs, plots, comparisons)?
  * Do they prefer French or technical terms?
* Collect **real use cases** and build reusable prompt templates.

---

## 📏 5. Define Validation Metrics for Agent Response

### When is a response "correct"?

| Dimension        | Validation Strategy                           |
| ---------------- | --------------------------------------------- |
| Data correctness | Compare data with manual query                |
| Interpretation   | Align with expert judgment                    |
| Relevance        | Based on whether question intent was captured |
| Clarity          | Language is simple, no ambiguity              |
| Visuals          | Plots match data and context described        |




``` mermaid
flowchart TD
    A(🖼️ User Input) --> G(Pre-model Hook)
    G --> B(📊 Data Analyst Agent)
    B --> H(💡 Create Suggestions)
    
    subgraph ExecutionOptions["Execution Options"]
        C(⚙️ Execute Code<br/>in Sandbox)
        D(🔍 Data Retriever<br/>Agent)
        I(📈 Data Visualiser)
    end
    
    ExecutionOptions --> |Process feedback| G
    B -.-> |Route request| ExecutionOptions
    
    B --> H(💡 Create Suggestions)
    H --> E(📊 Return Plot<br/>or Output)
    E --> F(💬 Respond to User)
    
    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    style G fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    style C fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    style D fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style I fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style H fill:#fff9c4,stroke:#f9a825,stroke-width:2px,color:#000
    style E fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style ExecutionOptions fill:#f5f5f5,stroke:#666,stroke-width:1px,color:#000

```    