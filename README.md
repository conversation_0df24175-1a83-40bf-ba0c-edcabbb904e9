# Energy Manager Copilot

A comprehensive energy management system built with FastAPI, LangChain, and Chainlit.

## Prerequisites

- Python 3.13 or higher
- Docker and Docker Compose
- UV package manager

## Installation

1. **Clone the repository**


2. **Set up Python environment with UV**
```bash
# Install UV if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create and activate virtual environment
uv venv
source .venv/bin/activate  # On Linux/Mac
# or
.venv\Scripts\activate  # On Windows

# Install dependencies
uv pip install -e .
```

3. **Set up environment variables**
Create a `.env` file in the root directory with the following variables:
```env
# Database Configuration
POSTGRES_USER=user_test
POSTGRES_PASSWORD=password_test
POSTGRES_DB=chainlit_db
POSTGRES_PORT=5432

# API Keys (Add your API keys here)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key
```

## Database Setup

1. **Start the PostgreSQL database**
```bash
docker compose up -d postgres_db
```

2. **Verify database connection**
```bash
# Check if the container is running
docker ps

# Check database logs
docker logs chainlit_postgres_db
```

The database will be automatically initialized with the schema defined in `init.sql`.

## Sandbox Setup

1. **Set up the Sandbox API**
```bash
# Navigate to the sandbox API directory
cd python-sandbox-api

# Build and start the sandbox service
docker compose up --build
```

The sandbox service will be available at `http://localhost:8004` and provides:
- Isolated execution environment
- Resource limits (CPU, memory)
- Network access control
- File system restrictions
- Session management
- Automatic cleanup of inactive sessions

### Sandbox Client Usage

The project includes a `SandboxClient` class for interacting with the sandbox service. Here's a basic example:

```python
from sandbox_client import SandboxClient

# Initialize the client
sandbox = SandboxClient(base_url="http://localhost:8004")

# Create a new session
session = await sandbox.create_session()

# Execute code in the sandbox
result = await sandbox.execute_code(
    session_id=session["session_id"],
    code="print('Hello from sandbox!')",
    timeout_seconds=30
)

# Upload files to the sandbox
await sandbox.upload_file(
    session_id=session["session_id"],
    file_path="path/to/file.csv",
    variable_name="data"
)

# Download plots from the sandbox
plot_data = await sandbox.download_plot(
    session_id=session["session_id"],
    plot_name="my_plot.png"
)
```

### Sandbox Security Considerations

1. **Resource Limits**
   - Set appropriate memory limits based on your needs
   - Configure CPU limits to prevent resource exhaustion
   - Monitor sandbox usage and adjust limits accordingly

2. **Network Access**
   - Disable network access if not required
   - Use allowlist for specific domains if needed
   - Monitor network usage for security

3. **Session Management**
   - Set appropriate session timeouts
   - Implement session cleanup for inactive sessions
   - Monitor session usage and adjust limits

4. **File System**
   - Restrict file system access to necessary directories
   - Implement file size limits
   - Monitor file operations for security

## Running the Application

1. **Start the Chainlit application**
```bash
chainlit run chainlit_app_simple.py
```

The application will be available at `http://localhost:8000`

# AGENTS Architechture


## Agent 1: Data Analyst

```mermaid
flowchart TD
    A(🖼️ User Input) --> G(Pre-model Hook)
    G --> B(📊 Data Analyst Agent)
    
    subgraph ExecutionOptions["Execution Options"]
        C(⚙️ Execute Code<br/>in Sandbox)
        D(🔍 Data Retriever<br/>Agent)
    end
    
    ExecutionOptions --> |Process feedback| G
    B -.-> |Route request| ExecutionOptions
    
    B --> E(📊 Return Plot<br/>or Output)
    E --> F(💬 Respond to User)
    
    style A fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    style G fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    style C fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    style D fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style B fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    style E fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style F fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style ExecutionOptions fill:#f5f5f5,stroke:#666,stroke-width:1px,color:#000
```

## Agent 2: Data Retriever

```mermaid
flowchart TD
    A(📄 Receive Data<br/>Description) --> B(🧠 RAG<br/>Selecting the right algos<br/>and labels)
    A --> C(✨ Generate Generic<br/>Parser Request)
    B --> C
    C --> D{Try Executing<br/>Request}
    D --> |Success ✓| E(📊 Data Description)
    D --> |Failure ✗| F(⚠️ Return Error to LLM)
    F --> |Retry| C
    E --> G(🎯 Return Data)
    
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    style B fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px,color:#000
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style D fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    style E fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    style F fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    style G fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
```

# Retry Mechanism Analysis and Improvements

## Overview

This document provides a comprehensive analysis of the current retry mechanism in `parser_graph.py` and detailed improvement suggestions to enhance reliability, performance, and maintainability of the data retrieval system.

## Current Retry Mechanism Analysis

### How It Works

The current retry mechanism is implemented through a LangGraph state machine with the following components:

```mermaid
flowchart TD
    A[User Request] --> B[Generate Request Node]
    B --> C[Process Request Node]
    C --> D{Check Errors}
    D -->|Errors >= 4| E[END - Max Retries]
    D -->|1-3 Errors| B
    D -->|No Errors| F[END - Success]
    
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style B fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style C fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style D fill:#ffebee,stroke:#c62828,stroke-width:2px
    style E fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    style F fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

### Current Implementation Details

1. **Retry Logic Location**: `create_generic_parser_graph()` function
2. **Retry Condition**: Hardcoded 4-error limit in `handle_after_processing()`
3. **Retry Strategy**: All retries go through LLM request regeneration
4. **Error Handling**: Basic error mapping in `process_generic_parser_request()`

### Error Categories Currently Handled

```python
error_mapping = {
    "GP_1.1": "MANDATORY_FIELD_EMTPY",
    "GP_1.2": "INVALID_PERIMETER_TYPE", 
    "GP_1.10": "INVALID_PERIMETER_LIST",
    "GP_1.3": "INVALID_DATE_FORMAT",
    "GP_1.4": "START_DATE_MUST_BE_BEFORE_END_DATE",
    "GP_1.5": "INVALID_GROUP_BY",
    "GP_1.6": "INVALID_GROUP_ID",
    "GP_1.7": "INVALID_COMPARISON_PERIOD",
}

# Additional error types:
# - CONNECTION_ERROR: Network issues
# - SERVER_ERROR: HTTP 4xx/5xx responses  
# - INVALID_REQUEST: JSON parsing failures
# - EMPTY_DETAILS: No data returned
```

## Current Issues and Limitations

### 1. **Fixed Retry Limits**
- **Issue**: Hardcoded 4-retry limit regardless of error type
- **Impact**: Transient network errors waste retries, while user errors retry unnecessarily
- **Example**: A temporary network glitch uses the same retry budget as a malformed request

### 2. **No Exponential Backoff**
- **Issue**: Immediate retries for all error types
- **Impact**: Can overwhelm servers during outages, doesn't allow transient issues to resolve
- **Example**: Server overload gets worse with immediate retries

### 3. **Inefficient Retry Strategy**
- **Issue**: All retries go through expensive LLM request regeneration
- **Impact**: Network errors shouldn't require LLM calls, wasting time and resources
- **Example**: A 500ms network timeout triggers a 2-3 second LLM regeneration

### 4. **No Error Classification**
- **Issue**: All errors treated identically
- **Impact**: Different error types need different handling strategies
- **Example**: User input errors vs. server errors need different approaches

### 5. **Missing Circuit Breaker**
- **Issue**: No protection against cascading failures
- **Impact**: System can get stuck in retry loops during widespread outages
- **Example**: API endpoint down causes all requests to retry indefinitely

### 6. **No Observability**
- **Issue**: No metrics on retry success rates or patterns
- **Impact**: Can't optimize retry strategies or detect systemic issues
- **Example**: Unknown if retries are actually helping or just adding latency

## Proposed Improvements

### 1. Smart Error Classification System

#### Implementation

```python
# retry/error_classifier.py
from enum import Enum
from typing import Dict, Any, Optional
import re

class ErrorCategory(Enum):
    TRANSIENT = "transient"      # Network, server errors - retry with backoff
    USER_INPUT = "user_input"    # Invalid parameters - retry with LLM
    PERMANENT = "permanent"      # Authentication, permissions - don't retry
    RATE_LIMIT = "rate_limit"    # Rate limiting - retry with longer backoff
    DATA_EMPTY = "data_empty"    # No data found - retry with modified params

class ErrorClassifier:
    def __init__(self):
        self.classification_rules = {
            # Transient errors - retry with exponential backoff
            ErrorCategory.TRANSIENT: [
                "CONNECTION_ERROR",
                "SERVER_ERROR",
                r"HTTP error 5\d\d",
                "TimeoutError",
                "ConnectionResetError"
            ],
            
            # User input errors - retry with LLM regeneration
            ErrorCategory.USER_INPUT: [
                "MANDATORY_FIELD_EMTPY",
                "INVALID_PERIMETER_TYPE",
                "INVALID_PERIMETER_LIST", 
                "INVALID_DATE_FORMAT",
                "START_DATE_MUST_BE_BEFORE_END_DATE",
                "INVALID_GROUP_BY",
                "INVALID_GROUP_ID",
                "INVALID_COMPARISON_PERIOD"
            ],
            
            # Permanent errors - don't retry
            ErrorCategory.PERMANENT: [
                r"HTTP error 401",  # Unauthorized
                r"HTTP error 403",  # Forbidden
                "INVALID_REQUEST"
            ],
            
            # Rate limiting - retry with longer backoff
            ErrorCategory.RATE_LIMIT: [
                r"HTTP error 429",
                "RATE_LIMIT_EXCEEDED"
            ],
            
            # Data empty - retry with modified parameters
            ErrorCategory.DATA_EMPTY: [
                "EMPTY_DETAILS"
            ]
        }
    
    def classify_error(self, error: str) -> ErrorCategory:
        """Classify error into appropriate category for retry strategy"""
        for category, patterns in self.classification_rules.items():
            for pattern in patterns:
                if re.search(pattern, error, re.IGNORECASE):
                    return category
        
        # Default to user input for unknown errors
        return ErrorCategory.USER_INPUT
    
    def get_retry_config(self, category: ErrorCategory) -> Dict[str, Any]:
        """Get retry configuration for error category"""
        configs = {
            ErrorCategory.TRANSIENT: {
                "max_retries": 5,
                "base_delay": 1.0,
                "max_delay": 60.0,
                "exponential_base": 2,
                "jitter": True,
                "use_llm": False
            },
            ErrorCategory.USER_INPUT: {
                "max_retries": 3,
                "base_delay": 0.5,
                "max_delay": 5.0,
                "exponential_base": 1.5,
                "jitter": False,
                "use_llm": True
            },
            ErrorCategory.PERMANENT: {
                "max_retries": 0,
                "base_delay": 0,
                "max_delay": 0,
                "exponential_base": 1,
                "jitter": False,
                "use_llm": False
            },
            ErrorCategory.RATE_LIMIT: {
                "max_retries": 3,
                "base_delay": 10.0,
                "max_delay": 300.0,
                "exponential_base": 2,
                "jitter": True,
                "use_llm": False
            },
            ErrorCategory.DATA_EMPTY: {
                "max_retries": 2,
                "base_delay": 1.0,
                "max_delay": 10.0,
                "exponential_base": 2,
                "jitter": False,
                "use_llm": True
            }
        }
        return configs.get(category, configs[ErrorCategory.USER_INPUT])
```

### 2. Exponential Backoff with Jitter

#### Implementation

```python
# retry/backoff.py
import random
import time
import asyncio
from typing import Optional

class ExponentialBackoff:
    def __init__(
        self,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        max_retries: int = 5
    ):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.max_retries = max_retries
        self.attempt = 0
    
    def calculate_delay(self) -> float:
        """Calculate delay for current attempt"""
        if self.attempt == 0:
            return 0
        
        # Calculate exponential delay
        delay = self.base_delay * (self.exponential_base ** (self.attempt - 1))
        delay = min(delay, self.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.jitter:
            delay = delay * (0.5 + random.random() * 0.5)
        
        return delay
    
    def should_retry(self) -> bool:
        """Check if should retry based on attempt count"""
        return self.attempt < self.max_retries
    
    def next_attempt(self) -> Optional[float]:
        """Move to next attempt and return delay"""
        if not self.should_retry():
            return None
        
        delay = self.calculate_delay()
        self.attempt += 1
        return delay
    
    async def wait(self) -> bool:
        """Wait for calculated delay, return True if should continue"""
        delay = self.next_attempt()
        if delay is None:
            return False
        
        if delay > 0:
            await asyncio.sleep(delay)
        return True
    
    def reset(self):
        """Reset attempt counter"""
        self.attempt = 0
```

### 3. Circuit Breaker Pattern

#### Implementation

```python
# retry/circuit_breaker.py
import time
from enum import Enum
from typing import Callable, Any, Optional
import asyncio

class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered

class CircuitBreaker:
    def __init__(
        self,
        failure_threshold: int = 5,
        timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = CircuitState.CLOSED
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (
            self.state == CircuitState.OPEN and
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.timeout
        )
    
    def _on_success(self):
        """Handle successful operation"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        """Handle failed operation"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        
        # Check if circuit is open
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception(f"Circuit breaker is OPEN. Service unavailable.")
        
        try:
            # Execute the function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Success - reset circuit breaker
            self._on_success()
            return result
            
        except self.expected_exception as e:
            # Failure - update circuit breaker state
            self._on_failure()
            raise e
    
    def get_state(self) -> dict:
        """Get current circuit breaker state"""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "last_failure_time": self.last_failure_time,
            "failure_threshold": self.failure_threshold
        }
```

### 4. Enhanced Retry Manager

#### Implementation

```python
# retry/retry_manager.py
import asyncio
import time
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from .error_classifier import ErrorClassifier, ErrorCategory
from .backoff import ExponentialBackoff
from .circuit_breaker import CircuitBreaker

@dataclass
class RetryAttempt:
    attempt_number: int
    error: str
    error_category: ErrorCategory
    delay: float
    timestamp: float
    success: bool = False

class RetryManager:
    def __init__(self):
        self.error_classifier = ErrorClassifier()
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_history: Dict[str, list] = {}
        self.metrics = {
            "total_attempts": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "category_stats": {}
        }
    
    def get_circuit_breaker(self, endpoint: str) -> CircuitBreaker:
        """Get or create circuit breaker for endpoint"""
        if endpoint not in self.circuit_breakers:
            self.circuit_breakers[endpoint] = CircuitBreaker(
                failure_threshold=5,
                timeout=60.0
            )
        return self.circuit_breakers[endpoint]
    
    async def execute_with_retry(
        self,
        func: Callable,
        request_id: str,
        endpoint: str = "default",
        *args,
        **kwargs
    ) -> Any:
        """Execute function with intelligent retry logic"""
        
        circuit_breaker = self.get_circuit_breaker(endpoint)
        backoff = None
        attempts = []
        
        while True:
            try:
                self.metrics["total_attempts"] += 1
                
                # Execute through circuit breaker
                result = await circuit_breaker.call(func, *args, **kwargs)
                
                # Success - record and return
                if attempts:
                    self.metrics["successful_retries"] += 1
                    attempts[-1].success = True
                
                self.retry_history[request_id] = attempts
                return result
                
            except Exception as e:
                error_str = str(e)
                error_category = self.error_classifier.classify_error(error_str)
                retry_config = self.error_classifier.get_retry_config(error_category)
                
                # Initialize backoff if first attempt
                if backoff is None:
                    backoff = ExponentialBackoff(**retry_config)
                
                # Record attempt
                attempt = RetryAttempt(
                    attempt_number=len(attempts) + 1,
                    error=error_str,
                    error_category=error_category,
                    delay=backoff.calculate_delay(),
                    timestamp=time.time()
                )
                attempts.append(attempt)
                
                # Update metrics
                category_key = error_category.value
                if category_key not in self.metrics["category_stats"]:
                    self.metrics["category_stats"][category_key] = {
                        "attempts": 0, "successes": 0, "failures": 0
                    }
                self.metrics["category_stats"][category_key]["attempts"] += 1
                
                # Check if should retry
                if not await backoff.wait():
                    # Max retries reached
                    self.metrics["failed_retries"] += 1
                    self.metrics["category_stats"][category_key]["failures"] += 1
                    self.retry_history[request_id] = attempts
                    raise e
                
                # For user input errors, regenerate request with LLM
                if retry_config["use_llm"] and "regenerate_request" in kwargs:
                    regenerate_func = kwargs["regenerate_request"]
                    if regenerate_func:
                        try:
                            new_request = await regenerate_func(error_str, attempts)
                            kwargs.update(new_request)
                        except Exception as regen_error:
                            print(f"Request regeneration failed: {regen_error}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get retry metrics"""
        return self.metrics.copy()
    
    def get_retry_history(self, request_id: str) -> list:
        """Get retry history for specific request"""
        return self.retry_history.get(request_id, [])
    
    def reset_metrics(self):
        """Reset metrics counters"""
        self.metrics = {
            "total_attempts": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "category_stats": {}
        }
```

### 5. Configuration-Based Retry Policies

#### Configuration File

```yaml
# config/retry_policies.yaml
retry_policies:
  default:
    transient:
      max_retries: 5
      base_delay: 1.0
      max_delay: 60.0
      exponential_base: 2.0
      jitter: true
      use_llm: false
    
    user_input:
      max_retries: 3
      base_delay: 0.5
      max_delay: 5.0
      exponential_base: 1.5
      jitter: false
      use_llm: true
    
    permanent:
      max_retries: 0
      base_delay: 0
      max_delay: 0
      exponential_base: 1
      jitter: false
      use_llm: false
    
    rate_limit:
      max_retries: 3
      base_delay: 10.0
      max_delay: 300.0
      exponential_base: 2.0
      jitter: true
      use_llm: false
    
    data_empty:
      max_retries: 2
      base_delay: 1.0
      max_delay: 10.0
      exponential_base: 2.0
      jitter: false
      use_llm: true

circuit_breaker:
  failure_threshold: 5
  timeout: 60.0
  
monitoring:
  enable_metrics: true
  log_retry_attempts: true
  alert_on_high_failure_rate: true
  failure_rate_threshold: 0.5
```

### 6. Integration with Existing Graph

#### Modified Graph Implementation

```python
# Enhanced parser_graph.py integration
from retry.retry_manager import RetryManager
from retry.error_classifier import ErrorCategory
import uuid

# Global retry manager instance
retry_manager = RetryManager()

def enhanced_process_request_node(state: GenericParserState) -> Dict[str, Any]:
    """Enhanced process request node with intelligent retry"""
    
    if not state.get("generated_request"):
        state["requests_error"] = state["requests_error"] + ["No generated request available."]
        return state
    
    request_json = state["generated_request"][-1]
    session_id = state.get("session_id")
    partner = state.get("partner")
    request_id = str(uuid.uuid4())
    
    if not partner:
        raise ValueError("Partner code is required in state for process_request_node")
    
    async def execute_request():
        """Wrapper function for retry manager"""
        return process_generic_parser_request(
            request_json, 
            partner=partner, 
            session_id=session_id
        )
    
    async def regenerate_request_func(error: str, attempts: list):
        """Function to regenerate request on user input errors"""
        errors = [attempt.error for attempt in attempts]
        return generate_request(
            state["user_request"][-1],
            errors=errors,
            previous_request=request_json,
            partner=partner
        )
    
    try:
        # Execute with intelligent retry
        response = asyncio.run(
            retry_manager.execute_with_retry(
                execute_request,
                request_id=request_id,
                endpoint=f"generic_parser_{partner}",
                regenerate_request=regenerate_request_func
            )
        )
        
        # Handle successful response (same as before)
        if isinstance(response, dict) and response.get("status") == "info_needed":
            state["needed_infos"] = response.get("needed_infos", "")
            state["input_data"] = []
            return state
        
        # Process successful response
        if isinstance(response, dict) and "input_data" in response:
            if "response_data" not in state:
                state["response_data"] = []
            state["response_data"] = state["response_data"] + [response]
            
            if "input_data" not in state:
                state["input_data"] = []
            
            state["input_data"] = state["input_data"] + [
                InputData(
                    variable_name=response["data_file_name"],
                    data_path=response["csv_path"],
                    data_description=response.get("data_description", ""),
                    sandbox_file_name=response.get("sandbox_file_name", "")
                )
            ]
        
        return state
        
    except Exception as e:
        # All retries exhausted - record final error
        state["requests_error"] = state["requests_error"] + [str(e)]
        
        # Add retry history to state for debugging
        retry_history = retry_manager.get_retry_history(request_id)
        if retry_history:
            state["retry_history"] = retry_history
        
        return state

def create_enhanced_generic_parser_graph():
    """Create enhanced graph with intelligent retry"""
    builder = StateGraph(GenericParserState)
    
    # Add nodes
    builder.add_node("generate_request", generate_request_node)
    builder.add_node("process_request", enhanced_process_request_node)
    
    # Add edges
    builder.add_edge(START, "generate_request")
    builder.add_edge("generate_request", "process_request")
    
    # Simplified conditional edge - retry logic now handled in retry manager
    def handle_after_processing(state: GenericParserState) -> str:
        errors = state["requests_error"]
        # Only end on error - no more retry logic here
        if len(errors) > 0:
            return END
        else:
            return END
    
    builder.add_conditional_edges("process_request", handle_after_processing)
    
    return builder.compile()
```

### 7. Monitoring and Metrics

#### Metrics Dashboard

```python
# monitoring/retry_dashboard.py
from typing import Dict, Any
import json
from datetime import datetime, timedelta

class RetryMetricsDashboard:
    def __init__(self, retry_manager):
        self.retry_manager = retry_manager
    
    def generate_report(self, time_window_hours: int = 24) -> Dict[str, Any]:
        """Generate comprehensive retry metrics report"""
        metrics = self.retry_manager.get_metrics()
        
        # Calculate success rates
        total_attempts = metrics["total_attempts"]
        successful_retries = metrics["successful_retries"]
        failed_retries = metrics["failed_retries"]
        
        success_rate = (
            (total_attempts - failed_retries) / total_attempts 
            if total_attempts > 0 else 0
        )
        
        retry_success_rate = (
            successful_retries / (successful_retries + failed_retries)
            if (successful_retries + failed_retries) > 0 else 0
        )
        
        # Category breakdown
        category_breakdown = {}
        for category, stats in metrics["category_stats"].items():
            category_breakdown[category] = {
                "attempts": stats["attempts"],
                "success_rate": (
                    (stats["attempts"] - stats["failures"]) / stats["attempts"]
                    if stats["attempts"] > 0 else 0
                ),
                "avg_retries": stats["attempts"] / max(1, stats["successes"] + stats["failures"])
            }
        
        # Circuit breaker status
        circuit_breaker_status = {}
        for endpoint, cb in self.retry_manager.circuit_breakers.items():
            circuit_breaker_status[endpoint] = cb.get_state
