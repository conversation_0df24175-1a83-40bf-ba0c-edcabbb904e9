import json
from typing import Any, Dict, List

from langchain_core.messages import HumanMessage, ToolMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.prebuilt import create_react_agent

# Avoid importing AgentState at module import time to prevent circular imports with main_copilot.
# We'll lazily fetch the schema inside create_coding_agent.

CODING_AGENT_SYSTEM_SUFFIX = """
You are a coding-only assistant. Your job is to generate Python code to accomplish the user's task using ONLY the datasets that are ALREADY LOADED as variables in the sandbox environment.

Rules:
- Do NOT call any tool except complete_python_task(python_code).
- Do NOT read files from disk and do NOT call pd.read_csv; datasets are already available as DataFrame variables. Use them directly by their variable names.
- Use only pandas, numpy, plotly, and sklearn if needed.
- Save Plotly figures into a list variable called plotly_figures (append each figure).
- Print concise outputs. Avoid fig.show().
- Code runs in a persistent sandbox with session_id and variables persist between runs.
- Do not attempt to discover or retrieve new datasets. Use only the given dataset variable names.
- Keep code self-contained and rely on existing variables, not file paths.
- The datasets names are between <>, ex: <response_20250802_035736>.

**Plotting Guidelines**
  - **Get more details**: Alway start by use df.describe() and df[column].value_counts() to get more infos about the datasets and use this infos to choose the apropriate plot (if there is outlier, ....).
  - **Aim for Excellence:** Always use Plotly to create visually compelling, insightful, and creatively designed charts.
  - **Always Use R Theme:** Configure all Plotly visualizations with the R theme (template='ggplot2') for sophisticated and professional-looking charts.
  - **Create Visually Astonishing Plots:** Generate visually stunning and aesthetically pleasing visualizations by:
      - Select the best plotting kind for the data (best way to represent the data).  
      - Using carefully selected color palettes that are both attractive and meaningful.
      - Including appropriate annotations, labels, and hover information.
      - Implementing thoughtful layout designs with proper spacing, fonts, and background elements.
      - Adding interactive elements that enhance user engagement without sacrificing clarity.
      - Trancate the lables (sites names, ..) if they are too long for better visuals.
      - Optimizing chart dimensions and proportions for maximum visual impact.
  - **Balance Creativity and Clarity:** Even innovative visualizations must remain easy for non-technical users to interpret.
  - **Purposeful Design:** Choose visualization types deliberately to highlight the key insights relevant to the user's goals.
  - **Implementation:**
      - Store all Plotly figures in a list called plotly_figures.
      - Append each figure with: plotly_figures.append(fig).
      - Briefly describe the contents of each plot in concise language.
      - Do not use fig.show().
"""

def build_prompt() -> ChatPromptTemplate:
    system = f"{CODING_AGENT_SYSTEM_SUFFIX}"
    prompt = ChatPromptTemplate.from_messages([
        ("system", system),
        ("placeholder", "{messages}"),
    ])
    return prompt

def create_coding_agent(model_instance):
    # Lazy import to avoid circular imports
    from tools import complete_python_task  # noqa: WPS433
    # Import AgentState lazily to break cycle: main_copilot -> tools -> agents.coding_agent -> main_copilot
    from main_copilot import AgentState  # noqa: WPS433

    prompt = build_prompt()
    tools = [complete_python_task]
    agent = create_react_agent(
        model=model_instance,
        tools=tools,
        prompt=prompt,
        state_schema=AgentState,
        checkpointer=None,
    )
    return agent

async def run_coding_agent(
    task_description: str,
    session_id: str,
    partner: str,
    model_instance,
) -> Dict[str, Any]:
    """
    Run a single-pass ReAct agent that must call complete_python_task with generated code.

    Inputs:
      - task_description: user task
      - session_id, partner: placed in config for tool execution
      - model_instance: LLM instance injected by caller to avoid circular imports

    Returns:
      - dict with stdout, plots, intermediate_outputs, and state
    """
    agent = create_coding_agent(model_instance)

    user_msg_text = (
        f"{task_description}\n\n"
        "Use the DataFrame variables that are already present in the sandbox directly; "
        "do not reload from files. Use df.describe() and df[column].value_counts() to "
        "inspect datasets when helpful. Generate code that completes the task and collects "
        "figures in a list called plotly_figures."
    )
    messages = [HumanMessage(content=user_msg_text)]

    config = {
        "configurable": {
            "session_id": session_id,
            "partner": partner,
        }
    }

    result_state = await agent.ainvoke({"messages": messages}, config=config)

    # Best-effort extraction of the last ToolMessage to summarize outputs.
    stdout = ""
    plots: List[str] = []
    intermediate_outputs: List[Dict[str, Any]] = []
    try:
        msgs = result_state.get("messages", [])
        # Find the last ToolMessage and parse json content
        for msg in reversed(msgs):
            if isinstance(msg, ToolMessage):
                try:
                    payload = json.loads(msg.content) if isinstance(msg.content, str) else msg.content
                    stdout = payload.get("stdout", stdout)
                    plots = payload.get("plots", plots)
                    break
                except Exception:
                    # ignore parse errors and continue
                    pass
    except Exception:
        pass

    return {
        "stdout": stdout,
        "plots": plots,
        "state": result_state,
    }