"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
import logging
from typing import Dict, List, Any, Optional
import traceback
# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")
import datetime

# Configure logging
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
logging.basicConfig(
    level=LOG_LEVEL,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
        logging.FileHandler('app.log')  # File output
    ]
)

# Create module-specific logger
logger = logging.getLogger(__name__)
###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer
# Optional Azure storage client import
try:
    from chainlit.data.storage_clients.azure import AzureStorageClient
    AZURE_AVAILABLE = True
except ImportError:
    AzureStorageClient = None
    AZURE_AVAILABLE = False

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage, AIMessageChunk
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langchain.schema.messages import BaseMessage
# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    create_main_graph,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient
from plot_template import apply_company_style  # Import the plot styling function

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

logger.info("Attempting to load .env from CWD")
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"



@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://"
        f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    logger.debug("Initialising SQLAlchemyDataLayer", extra={"component": "data_layer"})
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    logger.info("OAuth callback for provider", extra={"provider_id": provider_id})
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    """
    Parse ToolMessage content for display.
    - If content is JSON string → return parsed dict.
    - If content is plain string → treat as text without marking as error.
    - If content is dict → return as-is.
    - Else → return a textual representation without error flag.
    """
    if isinstance(content, str):
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            # Be tolerant of plain text ToolMessage content
            return {"text": content}
    if isinstance(content, dict):
        return content
    return {"text": str(content)}

def serialise_state(state: AgentState) -> dict:
    """Make AgentState printable (messages → small dicts)."""
    def _msg_to_dict(m):
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {
                "type": m.__class__.__name__,
                "id": getattr(m, "id", None),
                "content": m.content if isinstance(m.content, str) else "<complex>",
            }
        return str(m)

    # Create a copy of the state to avoid modifying the original
    serialized = dict(state)
    
    # Handle messages specially
    if "messages" in serialized:
        serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    
    # Ensure output_image_paths is preserved
    if "output_image_paths" in serialized:
        serialized["output_image_paths"] = list(serialized["output_image_paths"])
    
    return serialized

###############################################################################
# 5. LangGraph initialisation helper 
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""
    logger.debug("Initialising components", extra={"thread_id": thread_id, "component": "langgraph"})

    # 5.1  Checkpointer context manager
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:
        logger.error("Checkpointer setup failed", extra={"error": str(exc), "component": "langgraph"})
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent
    if cp_instance:
        lg_agent = create_main_graph(checkpointer=cp_instance, partner=partner_name) 
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
            "suggestions": [],
            "todos": []
        }

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set("langgraph_initialized_for_thread", bool(cp_instance))

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER")
    if not pn:
        await cl.Message(content="Erreur: Variable d'environnement DEFAULT_PARTNER non définie.").send()
        return
    logger.debug("on_chat_start", extra={"partner_name": pn, "component": "chat"})
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    cl.user_session.set("displayed_plot_filenames", set())  # Initialize empty set for tracking displayed plots

@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name") or os.getenv("DEFAULT_PARTNER")
    if not pn:
        await cl.Message(content="Erreur: Partenaire non défini pour cette conversation.").send()
        return
    await initialize_langgraph_components(tid, pn)

    cl.user_session.set("displayed_plot_filenames", set())  # Reset displayed plots set on resume



###############################################################################
# 8. Main message handler
###############################################################################

TOOL_DISPLAY_NAMES: Dict[str, str] = {
    "generic_parser": "🔍 Analyse des données",
    "complete_python_task": "🐍 Exécution du code Python",
    "coding_agent_tool": "📊 Coding Agent",
    "statistical_analysis": "📈 Analyse statistique",
    "file_processor": "📁 Traitement de fichiers",
    "web_scraper": "🌐 Extraction web",
    "database_query": "🗄️ Requête base de données",
    "ml_model": "🤖 Modèle d'apprentissage automatique",
    "text_analyzer": "📝 Analyse de texte",
    "image_processor": "🖼️ Traitement d'images",
    "write_todos":"📝 Planing"
}

TOOL_PROGRESS_MESSAGES: Dict[str, List[str]] = {
    "generic_parser": [
        "🔍 Analyse de la structure des données...",
        "🔍 Identification des patterns...",
        "🔍 Extraction des informations clés...",
    ],
    "complete_python_task": [
        "🐍 Préparation de l'environnement Python...",
        "🐍 Exécution du code en cours...",
        "🐍 Finalisation des calculs...",
    ],
    "data_visualization": [
        "📊 Préparation des données pour visualisation...",
        "📊 Génération des graphiques...",
        "📊 Application du style et finalisation...",
    ],
    "statistical_analysis": [
        "📈 Calcul des statistiques descriptives...",
        "📈 Analyse des corrélations...",
        "📈 Génération du rapport statistique...",
    ],
}

TASK_STATUS_EMOJIS = {
    "planning": "🤔",
    "executing": "⚡",
    "processing": "🔄",
    "analyzing": "🔍",
    "generating": "🎨",
    "completing": "✅",
    "error": "❌",
}


def get_tool_display_name(tool_name: str) -> str:
    """Return a pretty French label for *tool_name* (fallback → title‑case)."""
    return TOOL_DISPLAY_NAMES.get(tool_name, f"🔧 {tool_name.replace('_', ' ').title()}")


def get_progress_message(tool_name: str, step: int = 0) -> str:
    """Cycling progress message for *tool_name* (deterministic modulo length)."""
    msgs = TOOL_PROGRESS_MESSAGES.get(tool_name, [f"🔧 Exécution de {tool_name}..."])
    return msgs[step % len(msgs)]


def get_dynamic_agent_status(
    tool_names: List[str],
    current_step: str = "planning",
) -> str:
    """Tiny helper for agent status lines consumed by the UI."""
    emoji = TASK_STATUS_EMOJIS.get(current_step, "🤔")
    if current_step == "planning":
        return (
            f"{emoji} Planification : {tool_names[0]}"
            if len(tool_names) == 1
            else f"{emoji} Planification : {len(tool_names)} outils sélectionnés"
        )
    if current_step == "executing":
        return f"{emoji} Exécution en cours..."
    if current_step == "completing":
        return f"{emoji} Finalisation des résultats..."
    return f"{emoji} {current_step.title()}..."


# =============================================================================
# 2. ─── Safe object‑creation helpers ──────────────────────────────────────────
# =============================================================================


def get_tool_display_name(tool_name: str) -> str:
    """Return a pretty French label for tool_name (fallback → title‑case)."""
    return TOOL_DISPLAY_NAMES.get(tool_name, f"🔧 {tool_name.replace('_', ' ').title()}")

def get_progress_message(phase: str, tool_names: List[str] = None, current_tool: str = None) -> str:
    """Generate dynamic progress messages based on current phase"""
    if phase == "planning":
        if tool_names:
            return f"🧠 Planning to use: {', '.join(tool_names)}"
        return "🧠 Analyzing your request..."
    elif phase == "executing":
        if current_tool:
            return f"⚙️ Executing: {current_tool}"
        return "⚙️ Executing planned tasks..."
    elif phase == "processing":
        return "📊 Processing results..."
    elif phase == "finalizing":
        return "✨ Finalizing response..."
    else:
        return "🤔 Working on your request..."

async def create_step_safely(name: str, step_type: str, parent_id: str = None, input_content: str = "") -> Optional[cl.Step]:
    """Safely create a step with proper error handling and delays"""
    try:
        step = cl.Step(name=name, type=step_type, parent_id=parent_id)
        if input_content:
            step.input = input_content
        await step.send()
        # Small delay to ensure step is persisted before being used as parent
        await asyncio.sleep(0.1)
        return step
    except Exception as e:
        logger.error("Failed to create step", extra={"step_name": name, "error": str(e), "component": "ui"})
        return None

async def create_message_safely(content: str = "", author: str = "Assistant", parent_id: str = None, elements: List = None) -> Optional[cl.Message]:
    """Safely create a message with proper error handling"""
    try:
        msg = cl.Message(content=content, author=author, parent_id=parent_id)
        if elements:
            msg.elements = elements
        return msg
    except Exception as e:
        logger.error("Failed to create message", extra={"error": str(e), "component": "ui"})
        return None

async def send_suggestions(suggestions: List[str]):
    """Send actionable suggestions as buttons"""
    if not suggestions:
        return
    
    max_suggestions = 3
    limited_suggestions = suggestions[:max_suggestions]
    
    actions = []
    for i, suggestion in enumerate(limited_suggestions, 1):
        actions.append(
            cl.Action(
                name="suggestion",
                value=suggestion,
                label=f"💡 {suggestion}",
                description=f"Cliquez pour utiliser cette suggestion",
                payload={"text": suggestion}
            )
        )
    
    await cl.Message(
        content="",
        actions=actions
    ).send()
    
    logger.debug("Sent actionable suggestions", extra={"suggestion_count": len(limited_suggestions), "component": "ui"})

async def handle_plot_display(plot_path: str, active_thread_id: str, sandbox_client) -> Optional[cl.Plotly]:
    """Helper function to handle plot display with better error handling"""
    try:
        print(f"[DEBUG] Processing plot: {plot_path}")
        
        pickle_bytes = await sandbox_client.download_plot(
            session_id=active_thread_id,
            plot_name=plot_path,
        )
        
        if not pickle_bytes:
            print(f"[WARNING] No data received for plot: {plot_path}")
            return None
        
        fig_obj = pickle.loads(pickle_bytes)
        fig_obj = apply_company_style(fig_obj)
        
        plot_elem = cl.Plotly(
            name=os.path.basename(plot_path).rsplit(".", 1)[0],
            figure=fig_obj,
            display="inline",
        )
        
        print(f"[DEBUG] Successfully created plot element for: {plot_path}")
        return plot_elem
    except Exception as e:
        logger.error("Failed to process plot", extra={"plot_path": plot_path, "error": str(e), "component": "plot"})
        logger.debug("Traceback for plot processing error", extra={"traceback": traceback.format_exc(), "component": "plot"})
        return None

###############################################################################
# Action callback
###############################################################################

@cl.action_callback("suggestion")
async def handle_suggestion_click(action: cl.Action):
    """Called when the user clicks a suggestion button"""
    text = action.payload.get("text", "")
    logger.debug("Suggestion clicked", extra={"text": text, "component": "ui"})
    
    # Create a user message
    user_msg = cl.Message(
        content=text,
        author="User",
        type="user_message"
    )
    await user_msg.send()
    
    # Process the suggestion
    await on_message(user_msg)

###############################################################################
# Main message handler
###############################################################################

@cl.on_message
async def on_message(msg_event: cl.Message):
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        logger.critical("cl.context.session.thread_id is None in on_message", extra={"component": "chat"})
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        return
    
    logger.debug("Processing message", extra={"thread_id": active_thread_id, "component": "chat"})
    
    # --- Boiler-plate setup: Initialize if necessary ---
    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        logger.debug("Initialising LangGraph", extra={"thread_id": active_thread_id, "component": "langgraph"})
        partner_name_for_init = cl.user_session.get("partner_name") or cl.user_session.get_partner()
        if not partner_name_for_init:
            await cl.Message(content="Erreur: Partenaire non défini.").send()
            return
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)
    
    # --- Retrieve objects from session ---
    lg_agent = cl.user_session.get("lg_agent")
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")
    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient()
            cl.user_session.set("sandbox_client", sandbox_client)
        except Exception as e:
            await cl.Message(content=f"Erreur de configuration du client Sandbox: {e}").send()
            return
    
    if not lg_agent or lg_agent_state is None or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        return
    
    # --- Prepare for agent run ---
    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    messages_for_lg_agent_input = list(lg_agent_state.get("messages", [])) + [human_message_obj]
    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )
    
    # --- Create parent step first and ensure it's persisted ---
    agent_run_step = await create_step_safely(
        name="🤔 Analyzing your request...", 
        step_type="run", 
        parent_id=None,
        input_content=msg_event.content
    )
    
    if not agent_run_step:
        await cl.Message(content="Erreur: Impossible de créer l'étape principale.").send()
        return
    
    # Additional delay to ensure parent step is fully persisted
    await asyncio.sleep(0.3)
    
    # Progress tracking variables
    current_phase = "planning"
    planned_tools = []
    active_tool = None
    
    # Create assistant message variable (don't create it yet)
    assistant_final_msg = None
    all_plot_elements = []
    
    plots_from_previous_turns = set(lg_agent_state.get("output_image_paths", []))
    generated_plot_paths_this_turn = set()
    tool_steps: Dict[str, cl.Step] = {}
    messages_from_current_run = []
    current_run_lg_agent_state_dict = None
    
    final_plot_paths_from_run = None
    
    # Store suggestions to display AFTER the assistant message is complete
    pending_suggestions = None
    
    try:
        # --- Main agent streaming loop ---
        async for item in lg_agent.astream(
            {"messages": messages_for_lg_agent_input},
            config=config_for_run,
            stream_mode=["updates", "messages"],
            subgraphs=True,
        ):
            print(f"\n[DEBUG PAYLOAD] Item received: {item}")
            if len(item) >= 3:
                namespace_tuple, stream_mode, payload = item[:3]
            
            if namespace_tuple and namespace_tuple[-1].startswith("agent:"):
                if stream_mode == "updates":
                    if "agent" in payload:
                        for msg in payload["agent"].get("messages", []):
                            if msg not in messages_from_current_run:
                                messages_from_current_run.append(msg)
                            if isinstance(msg, AIMessage) and msg.tool_calls:
                                # Update to planning phase with tool names
                                if current_phase == "planning":
                                    tool_names = [get_tool_display_name(tc["name"]) for tc in msg.tool_calls]
                                    planned_tools = tool_names
                                    current_phase = "executing"
                                    
                                    # Update step with planned tools
                                    agent_run_step.name = "🧠 Planning Complete"
                                    agent_run_step.output = get_progress_message("planning", tool_names)
                                    await agent_run_step.update()
                                
                                for tc in msg.tool_calls:
                                    if tc["id"] not in tool_steps:
                                        tool_display_name = get_tool_display_name(tc['name'])
                                        active_tool = tool_display_name
                                        
                                        # Update main step to show current tool execution
                                        agent_run_step.name = f"⚙️ Executing: {tool_display_name}"
                                        agent_run_step.output = get_progress_message("executing", planned_tools, tool_display_name)
                                        await agent_run_step.update()
                                        
                                        # Create tool step with confirmed parent ID
                                        tool_step = await create_step_safely(
                                            name=f"🔧 {tool_display_name}", 
                                            step_type="tool", 
                                            parent_id=agent_run_step.id,
                                            input_content=f"```json\n{json.dumps(tc.get('args', {}), indent=2, ensure_ascii=False)}\n```"
                                        )
                                        
                                        if tool_step:
                                            tool_steps[tc["id"]] = tool_step
                                        else:
                                            print(f"[ERROR] Failed to create tool step for {tool_display_name}")
                    
                    if "tools" in payload:
                        current_run_lg_agent_state_dict = payload["tools"]
                        for msg in payload["tools"].get("messages", []):
                            if msg not in messages_from_current_run:
                                messages_from_current_run.append(msg)
                            if isinstance(msg, ToolMessage) and msg.tool_call_id in tool_steps:
                                ts = tool_steps[msg.tool_call_id]
                                if ts.output: 
                                    continue
                                content = _parse_tool_content(msg.content)
                                if isinstance(content, dict) and "error" in content:
                                    ts.name = f"❌ {ts.name.replace('🔧 ', '')}"
                                    ts.output = f"Error: {content['error']}"
                                    ts.is_error = True
                                else:
                                    ts.name = f"✅ {ts.name.replace('🔧 ', '')}"
                                    out_str = json.dumps(content, indent=2, ensure_ascii=False) if isinstance(content, (dict, list)) else str(content)
                                    ts.output = f"```json\n{out_str}```"
                                await ts.update()
                        
                        if "output_image_paths" in payload["tools"]:
                            # Update to processing phase when handling plots
                            if current_phase == "executing":
                                current_phase = "processing"
                                agent_run_step.name = "📊 Processing Results"
                                agent_run_step.output = get_progress_message("processing")
                                await agent_run_step.update()
                            
                            final_plot_paths_from_run = payload["tools"]["output_image_paths"]
                            current_figure_paths_set = {p for p in final_plot_paths_from_run}
                            new_plots_this_turn = current_figure_paths_set - plots_from_previous_turns
                            
                            for plot_path in new_plots_this_turn:
                                if plot_path in generated_plot_paths_this_turn: 
                                    continue
                                plot_elem = await handle_plot_display(plot_path, active_thread_id, sandbox_client)
                                if plot_elem:
                                    all_plot_elements.append(plot_elem)
                                    generated_plot_paths_this_turn.add(plot_path)
                
                elif stream_mode == "messages":
                    message, _ = payload
                    if isinstance(message, AIMessage) and message.content:
                        # Update to finalizing phase when generating response
                        if current_phase in ["executing", "processing"]:
                            current_phase = "finalizing"
                            agent_run_step.name = "✨ Finalizing Response"
                            agent_run_step.output = get_progress_message("finalizing")
                            await agent_run_step.update()
                        
                        # Create assistant message only when needed and with confirmed parent
                        if not assistant_final_msg:
                            assistant_final_msg = await create_message_safely(
                                content="", 
                                author="Assistant", 
                                parent_id=agent_run_step.id
                            )
                            if assistant_final_msg:
                                await assistant_final_msg.send()
                            else:
                                print("[ERROR] Failed to create assistant message")
                                continue
                        
                        if assistant_final_msg:
                            await assistant_final_msg.stream_token(message.content)
            
            elif not namespace_tuple and stream_mode == "updates":
                if (isinstance(payload, dict) and payload and "generate_suggestions" in payload and
                    isinstance(payload.get("generate_suggestions"), dict) and "suggestions" in payload["generate_suggestions"]):
                    suggestions = payload["generate_suggestions"]["suggestions"]
                    if suggestions:
                        # Store suggestions to display AFTER the assistant message is complete
                        pending_suggestions = suggestions
                        print(f"[DEBUG] Stored {len(suggestions)} suggestions to display after response")
    
    except Exception as e_stream:
        print(f"[ERROR CL STREAM] {e_stream}", file=sys.stdout)
        traceback.print_exc()
        agent_run_step.name = "❌ Error Occurred"
        agent_run_step.output = f"An unexpected error occurred: {e_stream}"
        agent_run_step.is_error = True
        await agent_run_step.update()
        await cl.Message(content=f"Une erreur inattendue s'est produite: {e_stream}").send()
        return
    
    finally:
        # Final step update
        if current_phase == "finalizing" or (assistant_final_msg and (assistant_final_msg.content or assistant_final_msg.elements)):
            agent_run_step.name = "✅ Task Completed"
            agent_run_step.output = "All tasks completed successfully"
        else:
            agent_run_step.name = "✅ Processing Complete"
            agent_run_step.output = "Request processed"
        await agent_run_step.update()
        
        # Handle final message and elements
        if all_plot_elements:
            if assistant_final_msg:
                assistant_final_msg.elements = all_plot_elements
            else:
                # Create assistant message for plots if none exists
                assistant_final_msg = await create_message_safely(
                    content="", 
                    author="Assistant", 
                    elements=all_plot_elements,
                    parent_id=agent_run_step.id
                )
                if assistant_final_msg:
                    await assistant_final_msg.send()
        
        if assistant_final_msg and (assistant_final_msg.content or assistant_final_msg.elements):
            if not assistant_final_msg.id:
                await assistant_final_msg.send()
            else:
                await assistant_final_msg.update()
        elif not assistant_final_msg:
            # Fallback message if nothing was created
            fallback_msg = await create_message_safely(
                content="✅ Task completed", 
                parent_id=agent_run_step.id
            )
            if fallback_msg:
                await fallback_msg.send()
        
        # Send actionable suggestions AFTER the assistant message is complete
        # Wait a bit to ensure the assistant message is fully rendered
        if pending_suggestions:
            await asyncio.sleep(0.5)  # Small delay to ensure proper ordering
            print(f"[DEBUG] Displaying {len(pending_suggestions)} actionable suggestions after response completion")
            await send_suggestions(pending_suggestions)
        
        # --- Persist agent state ---
        if lg_agent_state is not None:
            print("[DEBUG CL] Persisting agent state updates to user_session")
            
            merged = lg_agent_state.get("messages", []) + ensure_message_ids(messages_from_current_run)
            lg_agent_state["messages"], _ = remove_duplicate_messages(merged)
            
            if final_plot_paths_from_run is not None:
                print(f"[DEBUG CL] Persisting {len(final_plot_paths_from_run)} total plot paths to state.")
                lg_agent_state["output_image_paths"] = final_plot_paths_from_run
                
                # VALIDATION: Ensure cache contains all plots
                plot_cache = cl.user_session.get("plot_cache", {})
                missing_in_cache = [path for path in final_plot_paths_from_run 
                                if "_fig_" in os.path.basename(path) and path not in plot_cache]
                
                if missing_in_cache:
                    print(f"[WARNING CL] {len(missing_in_cache)} plots missing from cache: {missing_in_cache}")
            
            if current_run_lg_agent_state_dict:
                for key in ["current_variables", "data_description", "intermediate_outputs"]:
                    if key in current_run_lg_agent_state_dict:
                        lg_agent_state[key] = current_run_lg_agent_state_dict[key]
            
            # Set modified state back into session
            cl.user_session.set("lg_agent_state", lg_agent_state)
            
            # FINAL VALIDATION
            final_cache = cl.user_session.get("plot_cache", {})
            final_state = cl.user_session.get("lg_agent_state", {})
            print(f"[DEBUG CL] Final state - Cache: {len(final_cache)} plots, State: {len(final_state.get('output_image_paths', []))} paths")
            print(f"[DEBUG CL] Agent state updated successfully.")