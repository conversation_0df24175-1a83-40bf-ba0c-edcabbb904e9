import yaml
from collections import defaultdict
import sys

RESOLUTION_ORDER = ["10min", "hour", "day", "month", "year", "static", "unknown"]
rank = {r: i for i, r in enumerate(RESOLUTION_ORDER)}

def lowest(res_list, fallback="unknown"):
    if not res_list:
        return fallback
    known = [r for r in res_list if r in rank]
    return sorted(known, key=lambda r: rank[r])[0] if known else fallback

def summarize_partner(name, node, classes):
    algos = node.get("algorithms", [])

    # SENSOR: lowest resolution per fluid
    fluid_min = {}
    for a in algos:
        if a.get("class") != "SENSOR":
            continue
        hint = classes["SENSOR"].get("time_resolution_hint", [])
        for ind in a.get("indicators", []):
            res = lowest(ind.get("time_resolution") or a.get("time_resolution") or hint)
            for fl in ind.get("fluids", []):
                prev = fluid_min.get(fl)
                if prev is None or rank[res] < rank[prev]:
                    fluid_min[fl] = res

    # SITE: properties
    site_props = set()
    for a in algos:
        if a.get("class") != "SITE":
            continue
        for ind in a.get("indicators", []):
            site_props.add(ind["key"])

    # INVOICE: sample keys + suffixes
    invoice_keys, suffixes = set(), []
    for a in algos:
        if a.get("class") != "INVOICE":
            continue
        suffixes = a.get("electricity_period_suffixes", [])
        for ind in a.get("indicators", []):
            invoice_keys.add(ind["key"])

    # Output
    lines = [f"== {name} =="]

    lines.append("*SENSOR – time-series / aggregated measurements from sensors:*")
    if fluid_min:
        for fl, res in sorted(fluid_min.items(), key=lambda kv: rank[kv[1]]):
            lines.append(f"  - {fl}: time resolution down to [{res}]")
    else:
        lines.append("  - (no sensor indicators)")

    lines.append("")
    lines.append("*METER_SUPPLY – computed consumption from meter supply:*")
    lines.append("  - Units: m³ for WATER; kWh for others. Options: pci/pcs, no_prorata.")
    lines.append("  - Time resolution: [day, month]")

    lines.append("")
    lines.append("*INVOICE – invoice-derived metrics:*")
    if invoice_keys:
        sample = ", ".join(sorted(invoice_keys)[:8])
        more = " ..." if len(invoice_keys) > 8 else ""
        lines.append(f"  - Indicators (sample): {sample}{more}")
    if suffixes:
        lines.append(f"  - Electricity period suffixes: {', '.join(suffixes[:8])}{' ...' if len(suffixes)>8 else ''}")
    lines.append("  - Time resolution: [month]")

    lines.append("")
    lines.append("*COMMITMENT – contract KPIs and profit sharing:*")
    lines.append("  - KPIs: COM_1.1")
    lines.append("  - formula_type ∈ {real-NC, contract-N’B, real}")
    lines.append("  - Time resolution: [month, year]")

    lines.append("")
    lines.append("*SITE – static site metadata:*")
    if site_props:
        for k in sorted(site_props):
            lines.append(f"  - {k}")
    else:
        lines.append("  - (no site indicators)")

    return "\n".join(lines)

def summarize_yaml_file(partner_name):
    yaml_path="config/partner_prompts_2.yaml"
    with open(yaml_path, "r", encoding="utf-8") as f:
        doc = yaml.safe_load(f)

    classes = doc.get("classes", {})
    partner_node = doc.get(partner_name)
    if not partner_node:
        print(f"Partner '{partner_name}' not found in {yaml_path}")
        sys.exit(1)

    print(summarize_partner(partner_name, partner_node, classes))

summarize_yaml_file("groupeherve_agences")