classes:
  SENSOR:
    short: "SENSOR – time-series / aggregated measurements from meters & sensors"
    description: "Real-time and historical consumption data from various sensor types and meters"
    keywords: [sensor, meter, label, consumption, aggregated, time-series, fluid, real-time]
    time_resolutions: [10min, hour, day, month]
    fluids_supported: [ELEC, GAS, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER]
    data_freshness: real-time

  METER_SUPPLY:
    short: "METER_SUPPLY – computed consumption from meter supply data"
    description: "Processed consumption calculations with normalization options"
    keywords: [supply, computed, consumption, normalization, pci, pcs, prorata, linearization]
    time_resolutions: [day, month]
    fluids_supported: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, <PERSON>OLAR, OTHER, ALL]
    data_freshness: processed

  INVOICE:
    short: "INVOICE – billing and cost data from energy supplier invoices"
    description: "Normalized invoice data including consumption, costs, taxes, and billing periods"
    keywords: [invoice, cost, billing, vat, tax, period, emissions, supplier, price]
    time_resolutions: [month]
    fluids_supported: [ELEC, GAS, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY, ALLW]
    data_freshness: monthly

  COMMITMENT:
    short: "COMMITMENT – contract performance indicators and profit sharing"
    description: "Energy performance metrics from contracts and profit-sharing calculations"
    keywords: [commitment, contract, performance, kpi, profit, intéressement, target, actual]
    time_resolutions: [month, year]
    fluids_supported: [ELEC, GAS, HEAT, COLD, ALL_ENERGY]
    data_freshness: contractual

  SITE:
    short: "SITE – static site properties and metadata"
    description: "Immutable site characteristics and geographical information"
    keywords: [site, metadata, properties, typology, area, location, static]
    time_resolutions: [static]
    fluids_supported: []
    data_freshness: static

# Partner-specific algorithms
partners:
  groupeherve_alerteo:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS, WATER]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_10min_enedis:
            description: "Electric consumption data from 'conso_10min_enedis' meters"
            fluid: ELEC
            resolution: 10min
          conso_mensuelle_enedis:
            description: "Electric consumption data from 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "Gas consumption data from 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day
          energie_info_grdf:
            description: "Gas consumption data from 'energie_info_grdf' meters"
            fluid: GAS
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  groupeherve_hervethemique:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS, WATER]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_10min_enedis:
            description: "Electric consumption data from 'conso_10min_enedis' meters"
            fluid: ELEC
            resolution: 10min
          conso_mensuelle_enedis:
            description: "Electric consumption data from 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "Gas consumption data from 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day
          energie_info_grdf:
            description: "Gas consumption data from 'energie_info_grdf' meters"
            fluid: GAS
            resolution: hour
          conso_enedis:
            description: "Electric consumption data from 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL, ALL_ENERGY, ALLW]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Electricity power exceedance cost"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice (use when no fluid is specified)"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  groupeherve_agences:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS, WATER]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_10min_enedis:
            description: "Electric consumption data from 'conso_10min_enedis' meters"
            fluid: ELEC
            resolution: 10min
          conso_mensuelle_enedis:
            description: "Electric consumption data from 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "Gas consumption data from 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day
          energie_info_grdf:
            description: "Gas consumption data from 'energie_info_grdf' meters"
            fluid: GAS
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification
  oksigen:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          energie_facturante_grdf:
            description: "to get the electric consumption data of 'grdf' meters"
            fluid: GAS
            resolution: day
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'enedis' meters"
            fluid: ELEC
            resolution: month

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  engie_ec_ent_em:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          enedis_dm:
            description: "to get the electric consumption data of 'enedis_dm' meters"
            fluid: ELEC
            resolution: hour
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          cdc_enedis:
            description: "to get the electric power data of 'cdc_enedis' meters"
            fluid: ELEC
            resolution: hour

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  engie_ec_gc_prive:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          enedis_dm:
            description: "to get the electric consumption data of 'enedis_dm' meters"
            fluid: ELEC
            resolution: hour
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          cdc_enedis:
            description: "to get the electric power data of 'cdc_enedis' meters"
            fluid: ELEC
            resolution: hour

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  engie_ec_gc_publics:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          enedis_dm:
            description: "to get the electric consumption data of 'enedis_dm' meters"
            fluid: ELEC
            resolution: hour
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          cdc_enedis:
            description: "to get the electric power data of 'cdc_enedis' meters"
            fluid: ELEC
            resolution: hour

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  siplec:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          puissance_enedis:
            description: "to get the electric power data of 'puissance_enedis' meters"
            fluid: ELEC
            resolution: hour

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Electricity power exceedance cost"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount invoiced"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification
  cyrisea_cd92:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "to get the gas consumption data of 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY, ALLW]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with gas or electricity consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Cost related to electricity power exceedance"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice (use when no fluid is specified)"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  alterea:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "to get the gas consumption data of 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY, ALLW]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Electricity power exceedance cost"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice (use when no fluid is specified)"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  cyrisea_cd74:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "to get the gas consumption data of 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY, ALLW]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Electricity power exceedance cost"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice (use when no fluid is specified)"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification            
  demathieu_bard:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  demathieu_bard_chantiers:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification

  demathieu_bard_patrimoine:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification
  vincienergies:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for all fluids"
        description: "Retrieves aggregated sensor data for all fluid types from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          energie_facturante_grdf:
            description: "to get the gas consumption data of 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "Total site area in square meters"
            category: physical
            unit: m²
          site.typology:
            description: "Site classification/typology"
            category: classification
  helexia:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption/power by label"
        description: "Retrieves aggregated sensor data for electric consumption and power (and other fluids if present) from labeled meters"
        keywords: [aggregated, sensor, label, consumption, power]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour
          dm:
            description: "to get the electric consumption data of 'dm' meters"
            fluid: ELEC
            resolution: hour
          conso:
            description: "to get the electric consumption data of 'conso' meters"
            fluid: ELEC
            resolution: hour
          "10_min":
            description: "to get the electric consumption data of '10_min' meters"
            fluid: ELEC
            resolution: 10min
          elec_act_cons:
            description: "to get the electric consumption data of 'elec_act_cons' meters"
            fluid: ELEC
            resolution: hour
          consumption_elec:
            description: "to get the electric consumption data of 'consumption_elec' meters"
            fluid: ELEC
            resolution: hour
          cdc:
            description: "to get the electric power data of 'cdc' meters"
            fluid: ELEC
            resolution: hour

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification

  helexia_demo:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label"
        description: "Retrieves aggregated sensor data for electric consumption from labeled meters"
        keywords: [aggregated, sensor, label, consumption]
        fluids_applicable: [ELEC]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour
          dm:
            description: "to get the electric consumption data of 'dm' meters"
            fluid: ELEC
            resolution: hour
          conso:
            description: "to get the electric consumption data of 'conso' meters"
            fluid: ELEC
            resolution: hour
          elec_act_cons:
            description: "to get the electric consumption data of 'elec_act_cons' meters"
            fluid: ELEC
            resolution: hour
          consumption_elec:
            description: "to get the electric consumption data of 'consumption_elec' meters"
            fluid: ELEC
            resolution: hour
          elec_cons:
            description: "to get the electric consumption data of 'elec_cons' meters"
            fluid: ELEC
            resolution: hour

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification

  greenbirdie:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption by label for ELEC & GAS"
        description: "Retrieves aggregated sensor data for electricity and gas from labeled meters"
        keywords: [aggregated, sensor, label, consumption, enedis, grdf]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_mensuelle_enedis:
            description: "to get the electric consumption data of 'conso_mensuelle_enedis' meters"
            fluid: ELEC
            resolution: month
          conso_enedis:
            description: "to get the electric consumption data of 'conso_enedis' meters"
            fluid: ELEC
            resolution: hour
          conso_annuelle_enedis:
            description: "to get the electric consumption data of 'conso_annuelle_enedis' meters"
            fluid: ELEC
            resolution: year
          energie_annuelle_grdf:
            description: "to get the gas consumption data of 'energie_annuelle_grdf' meters"
            fluid: GAS
            resolution: year
          energie_facturante_grdf:
            description: "to get the gas consumption data of 'energie_facturante_grdf' meters"
            fluid: GAS
            resolution: day

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification

  spiefacilities:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated consumption & environmental metrics by label"
        description: "Retrieves aggregated data for electricity, gas, water, and environmental metrics (temperature, HDD/CDD)"
        keywords: [aggregated, sensor, label, consumption, temperature, HDD, CDD, DJU, DJF]
        fluids_applicable: [ELEC, GAS, WATER, ALL]
        time_resolution_available: [hour, day, month]
        min_resolution: hour
        indicator_options:
          DJF_PRO_26:
            description: "CDD (cooling degree days) with 26°C reference"
            fluid: ALL
            resolution: day
          DJU_PRO_18_COSTIC:
            description: "HDD (heating degree days) with 18°C reference (COSTIC method)"
            fluid: ALL
            resolution: day
          GENERAL_EAU_CONSO:
            description: "Site total consumption of water"
            fluid: WATER
            resolution: day
          GENERAL_GAZ_CONSO:
            description: "Site total consumption of gas"
            fluid: GAS
            resolution: day
          TEMPERATURE_AMBIANTE:
            description: "Outdoor temperature recorded at a site"
            fluid: ALL
            resolution: hour
          GENERAL_ELEC_CONSO:
            description: "Site total consumption of electricity"
            fluid: ELEC
            resolution: day

      - id: consumption_meter_supply
        class: METER_SUPPLY
        mode: out
        short: "Computed consumption from meter supply with normalization options"
        description: "Computes consumption from meter supply data. Units: m³ for WATER, kWh for others. Supports PCI/PCS normalization and prorata options. This is a flexible indicator name and must be composed with parts:

          • Fluid code (**mandatory**):
            Must be appended after a dash (-).
            Example: `consumption_meter_supply-ELEC`

          • Fluid option (**optional**):
            Can be `pci` or `pcs`, and is added as a prefix with an underscore.
            Example: `consumption_meter_supply_pci-GAS`

          • Prorata option (**optional**):
            Can be `no_prorata`, appended after another dash following the fluid code.
            Example: `consumption_meter_supply_pci-GAS-no_prorata`

          Final format: `consumption_meter_supply[_fluid_option]-FLUID_CODE[-prorata_option]`"
        keywords: [supply, computed, normalization, pci, pcs, prorata, meter]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        units:
          WATER: m³
          default: kWh
        indicator_pattern:
          format: "consumption_meter_supply[_{fluid_option}]-{FLUID}[-{prorata_option}]"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
          fluids: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL]
        normalization_fluids: [GAS, WOOD, FOL, FOD, COAL, PROPANE, LPG]
        indicator_options:
          consumption_meter_supply:
            description: "Flexible indicator built from parts; see indicator_pattern"

      - id: meter_labels
        class: SENSOR
        mode: in
        short: "Consumption by meter label with optional normalization/prorata"
        description: "Returns consumption (same unit as CMD) for all meters sharing a label, with optional PCI/PCS and prorata flags"
        keywords: [sensor, meter, label, consumption, pci, pcs, prorata]
        fluids_applicable: [ELEC, GAS, WATER, OTHER, ALL]
        time_resolution_available: [day, month]
        min_resolution: day
        indicator_pattern:
          format: "[fluid_option-][prorata_option-]label"
          fluid_options: [pci, pcs]
          prorata_options: [no_prorata]
        indicator_options:
          meter_labels:
            description: "Build as [fluid_option-][prorata_option-]label (e.g., pci-no_prorata-my-label)"

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification

  energisme:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor data by label for power/consumption"
        description: "Retrieves aggregated sensor data for electric power/consumption and gas consumption from labeled meters"
        keywords: [aggregated, sensor, label, consumption, power]
        fluids_applicable: [ELEC, GAS]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          conso_elec:
            description: "to get the electric consumption data of 'conso_elec' meters"
            fluid: ELEC
            resolution: hour
          conso_gaz:
            description: "to get the electric consumption data of 'conso_gaz' meters"
            fluid: GAS
            resolution: hour

      - id: commitment
        class: COMMITMENT
        mode: out
        short: "Contract performance indicators and KPI calculations"
        description: "Computes energy performance indicators from commitments with aggregation options. indicator_options
          **Format:**
            COM-energy#Suivi P1 - chaleur#empty#<formula_type>

          **Field breakdown:**
            - formula_type (Mandatory): can be `real-NC`, `contract-N'B`, `real` (must be specified in the request).
              - 'real-NC': consommation réelle allouée à l'usage chauffage.
              - 'contract-N'B': cible contractuelle pour la consommation de chauffage

          **IMPRORTANT**:  La performance du contrat se calcul comme ((NC/N'B)-1)*100, exemple:
            - \"formula\": \"{commitment#out:COM-energy#p1_tracking_heat##real-NC}- {commitment#out:COM-energy#p1_tracking_heat##contract-N'B}\"

          Example:
            COM-energy#Suivi P1 - chaleur#empty#contract-N'B-cum"
        keywords: [commitment, contract, performance, kpi, formula, target, actual]
        time_resolution_available: [month, year]
        min_resolution: month
        formula_types: ["real-NC", "contract-N'B", "real"]
        indicator_options:
          COM_1.1:
            description: "Global commitment performance calculation"
            format: "COM-energy#Suivi P1 - chaleur#empty#<formula_type>"
            category: performance

      - id: profit_sharing_multisite
        class: COMMITMENT
        mode: out
        short: "Profit sharing calculations for commitments"
        description: "Calculates operator and client profit (intéressement) for commitments"
        keywords: [profit, intéressement, commitment, operator, client]
        time_resolution_available: [month, year]
        min_resolution: month
        indicator_options:
          operator_profit:
            description: "Operator profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_operator_profit}"
            category: profit
          client_profit:
            description: "Client profit from commitment"
            formula: "{profit_sharing_multisite#out:energy#p1_tracking_heat##contract-currency_client_profit}"
            category: profit

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification
  emera:
    algorithms:
      - id: sum_sensor_labels
        class: SENSOR
        mode: in
        short: "Aggregated sensor consumption/power by label"
        description: "Retrieves aggregated sensor data for electricity, gas, heat, and submetering from labeled meters"
        keywords: [aggregated, sensor, label, consumption, power, enedis, grdf, heat, submetering]
        fluids_applicable: [ELEC, GAS, HEAT, ALL]
        time_resolution_available: [10min, hour, day, month]
        min_resolution: 10min
        indicator_options:
          partfixegaz:
            description: "to get fixed gas share data from 'partfixegaz'"
            fluid: GAS
            resolution: month
          puissance_enedis:
            description: "to get the electric power data of 'puissance_enedis' meters"
            fluid: ELEC
            resolution: hour
          energie_facturante_lin_grdf:
            description: "to get the gas consumption data of 'energie_facturante_lin_grdf' meters"
            fluid: GAS
            resolution: day
          conso_chaleur_agelia:
            description: "to get the heat consumption data of 'conso_chaleur_agelia' meters"
            fluid: HEAT
            resolution: day
          conso_chaleur:
            description: "to get the heat consumption data of 'conso_chaleur' meters"
            fluid: HEAT
            resolution: day
          emera-cta2024:
            description: "to get submetering data from 'emera-cta2024'"
            fluid: ALL
            resolution: hour

      - id: invoicing_statistics
        class: INVOICE
        mode: out
        short: "Invoice normalization for consumption, costs, and emissions"
        description: "Normalizes energy supplier invoices to extract consumption, cost, and emission data over billing periods. **IMPRORTANT**: when choosing the indicators (except for the ones that contains elec like invoice.elec_cost_cmdps) You must append the fluid code after a dash (-).
          Valid fluids: ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL,PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY."
        keywords: [invoice, cost, billing, vat, tax, emissions, supplier, period]
        fluids_applicable: [ELEC, WATER, STEAM, HOT_WATER, COMPRESSED_AIR, GAS, FOD, FOL, PROPANE, LPG, WOOD, COAL, HEAT, COLD, SOLAR, OTHER, ALL_ENERGY, ALLW]
        time_resolution_available: [month]
        min_resolution: month
        requires_fluid_suffix: true
        electricity_periods:
          - peak_hour
          - winter_peak_hour
          - winter_off_peak_hour
          - summer_peak_hour
          - summer_off_peak_hour
          - single_period
          - full_hour
          - off_peak_hour
          - half_season_peak_hour
          - half_season_off_peak_hour
          - july_august
          - white_full_hour
          - white_off_peak_hour
          - red_full_hour
          - red_off_peak_hour
          - blue_full_hour
          - blue_off_peak_hour
        indicator_options:
          invoice.cost_consumption:
            description: "Cost associated with gas or electricity consumption"
            requires_fluid: true
            category: cost
          invoice.elec_cost_cmdps:
            description: "Cost related to electricity power exceedance"
            requires_fluid: false
            category: cost
            fluid: ELEC
          invoice.consumption_invoiced:
            description: "Energy consumption in kWh from invoice (use when no fluid is specified)"
            requires_fluid: false
            category: consumption
          invoice.elec_consumption:
            description: "Electricity consumption by time period"
            requires_fluid: false
            requires_period: true
            category: consumption
            fluid: ELEC
          emission_invoiced:
            description: "Emissions data (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          ghg_ktco2:
            description: "Greenhouse gas emissions in ktCO₂ (requires fluid suffix)"
            requires_fluid: true
            category: emissions
          invoice.elec_cost:
            description: "Electricity cost by time period"
            requires_fluid: false
            requires_period: true
            category: cost
            fluid: ELEC
          invoice.cost_total_exc_vat:
            description: "Total cost excluding VAT"
            requires_fluid: false
            category: cost
          invoice.cost_total_inc_vat:
            description: "Total cost including VAT"
            requires_fluid: false
            category: cost
          invoice.date:
            description: "Invoice date"
            requires_fluid: false
            category: metadata
          invoice.delivery_point:
            description: "Delivery point (PDL)"
            requires_fluid: false
            category: metadata
          invoice.invoicing_start_date:
            description: "Start date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.invoicing_end_date:
            description: "End date of invoicing period"
            requires_fluid: false
            category: metadata
          invoice.cost_total_vat:
            description: "Total VAT amount invoiced"
            requires_fluid: false
            category: cost
          invoice.cost_obligation_cee:
            description: "CEE obligation cost"
            requires_fluid: false
            category: cost
          invoice.price_consumption:
            description: "Electricity unit price per kWh"
            requires_fluid: false
            category: price
          invoice.cost_fixed_charge:
            description: "Fixed subscription cost"
            requires_fluid: false
            category: cost
          invoice.cost_supply:
            description: "Transmission/delivery cost"
            requires_fluid: false
            category: cost
          invoice.cost_tax:
            description: "Total tax amount"
            requires_fluid: false
            category: cost

      - id: site
        class: SITE
        mode: out
        short: "Site properties and metadata retrieval"
        description: "Retrieves static site characteristics and properties"
        keywords: [site, metadata, properties, area, typology]
        time_resolution_available: [static]
        min_resolution: static
        indicator_options:
          site.total_area_m2:
            description: "to get total area of the site in square meter"
            category: physical
            unit: m²
          site.typology:
            description: "to get the typology of the site"
            category: classification