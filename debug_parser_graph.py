#!/usr/bin/env python3

import sys
import traceback

print("Starting debug of parser_graph.py...")

try:
    print("Step 1: Importing basic modules...")
    import json
    import httpx
    import requests
    import csv
    import os
    import time
    import threading
    from datetime import datetime, timed<PERSON>ta
    from typing import TypedDict, Dict, Any, List, Optional, Literal, Union
    from langgraph.graph import StateGraph, START, END
    import operator
    from typing import Annotated
    from langchain_google_genai import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from langchain_anthropic import ChatAnthropic
    from langchain_deepseek import ChatDeepSeek
    from langchain_openai import ChatOpenAI
    from enum import Enum
    from dataclasses import dataclass, field
    import traceback
    import logging
    from abc import ABC, abstractmethod
    from dotenv import load_dotenv
    print("Basic imports successful")

    print("Step 2: Loading environment...")
    load_dotenv()
    print("Environment loaded")

    print("Step 3: Setting up logging...")
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    print("Logging setup complete")

    print("Step 4: Getting environment variables...")
    GENERIC_PARSER_URL = os.getenv("GENERIC_PARSER_URL")
    print(f"GENERIC_PARSER_URL: {GENERIC_PARSER_URL}")

    print("Step 5: Defining ErrorType enum...")
    class ErrorType(Enum):
        NETWORK = "network"
        SERVER = "server"
        VALIDATION = "validation"
        USER_INPUT = "user_input"
        SYSTEM = "system"
        EMPTY_DATA = "empty_data"
        CRITICAL = "critical"
    print("ErrorType defined")

    print("Step 6: Defining ErrorInfo dataclass...")
    @dataclass
    class ErrorInfo:
        error_type: ErrorType
        error_code: str
        message: str
        is_retryable: bool
        requires_llm_regeneration: bool
        max_retries: int
        backoff_multiplier: float = 1.0
        suggested_action: Optional[str] = None
    print("ErrorInfo defined")

    print("Step 7: Defining ErrorClassifier...")
    class ErrorClassifier:
        """Classification intelligente des erreurs"""
        
        ERROR_MAPPINGS = {
            "GP_1.1": ErrorInfo(ErrorType.VALIDATION, "GP_1.1", "Champ obligatoire vide", True, True, 2, 1.0, "Vérifier les champs obligatoires"),
        }
        
        @classmethod
        def classify_error(cls, error):
            return ErrorInfo(ErrorType.SYSTEM, "UNKNOWN", str(error), False, False, 0)
    print("ErrorClassifier defined")

    print("Step 8: Defining CircuitState and CircuitBreaker...")
    class CircuitState(Enum):
        CLOSED = "closed"
        OPEN = "open"
        HALF_OPEN = "half_open"

    class CircuitBreaker:
        def __init__(self, failure_threshold=5, recovery_timeout=60):
            self.failure_threshold = failure_threshold
            self.recovery_timeout = recovery_timeout
            self.failure_count = 0
            self.state = CircuitState.CLOSED
            self.last_failure_time = None

        def get_state(self):
            return {"state": self.state.value, "failure_count": self.failure_count}

        def __call__(self, func):
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return wrapper
    print("CircuitBreaker defined")

    print("Step 9: Defining RetryStrategy...")
    class RetryStrategy(ABC):
        @abstractmethod
        def should_retry(self, attempt: int, error_info: ErrorInfo) -> bool:
            pass

        @abstractmethod
        def get_delay(self, attempt: int, error_info: ErrorInfo) -> float:
            pass

    class ExponentialBackoffStrategy(RetryStrategy):
        def __init__(self, base_delay: float = 1.0, max_delay: float = 60.0, jitter: bool = True):
            self.base_delay = base_delay
            self.max_delay = max_delay
            self.jitter = jitter

        def should_retry(self, attempt: int, error_info: ErrorInfo) -> bool:
            return attempt < error_info.max_retries

        def get_delay(self, attempt: int, error_info: ErrorInfo) -> float:
            return min(self.base_delay * (2 ** attempt), self.max_delay)
    print("RetryStrategy defined")

    print("Step 10: Defining RetryManager...")
    class RetryManager:
        def __init__(self, strategy: RetryStrategy = None):
            self.strategy = strategy or ExponentialBackoffStrategy()
            self.circuit_breaker = CircuitBreaker(failure_threshold=5, recovery_timeout=60)

        def execute_with_retry(self, func):
            return func()
    print("RetryManager defined")

    print("Step 11: Creating retry_manager instance...")
    retry_manager = RetryManager(ExponentialBackoffStrategy(base_delay=1.0, max_delay=30.0))
    print("retry_manager created successfully")

    print("Step 12: Testing access...")
    print(f"ErrorClassifier available: {ErrorClassifier}")
    print(f"retry_manager available: {retry_manager}")

    print("All steps completed successfully!")

except Exception as e:
    print(f"Error occurred: {e}")
    traceback.print_exc()
