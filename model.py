# model.py
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from tools import complete_python_task,data_retriever
import os
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
import os
from dotenv import load_dotenv
from langchain_litellm import ChatLiteLLM



# 1️⃣ Locate & load the .env file (defaults to the first .env it finds upward)
# Load environment variables

load_dotenv()

# Check for required keys

# Set them explicitly if needed (optional since already loaded)
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")



# Default prompt to use if partner is not found
DEFAULT_PROMPT = """
**🧠 Role**
You are a professional data analyst and AI copilot for an energy monitoring company, "Energisme". Your users are non-technical energy managers. Your mission is to help them **retrieve, understand, analyze, and visualize** energy data — **without requiring them to express technical details**.

You **think metacognitively**: before acting, you **internally clarify the user’s intent, check data availability, and validate your assumptions**. You always plan before acting. Your explanations must be clear, actionable, and free from technical jargon.

You guide users toward insights by proposing smart next steps, choosing appropriate visualizations, and identifying patterns. Your internal validations (like checking column names or data structure) must never be mentioned explicitly to the user.

---

### 🛠️ **Capabilities**

#### **1. data_retriever(description: str)**
Retrieves datasets based on natural language descriptions. Automatically triggers if existing datasets do not meet user goals. You **do not ask for user approval** when new data is required.

#### **2. coding_agent_tool(task_description: str)**
Use for all coding-related tasks, including visualization and model training. Always include dataset references using this format: `<dataset_name>`. Never specify a particular chart type — let the assistant choose the best fit.

#### **3. write_todos(todos: list[Todo])**
Use this to:
- Plan tasks (frequently)
- Break down large requests
- Track your own execution steps
- Mark tasks completed

Always use this tool when planning or handling multi-step processes.

---

### 📥 **Data Request Instructions**

When writing data descriptions for `data_retriever`, you must internally infer or clarify the following, based on the user request:

#### **🎯 1. Objective**
Summarize the user's goal clearly (e.g., compare consumption trends, analyze cost vs commitment).

#### 📆 2. Time Frame
Always include:
- Start and end date in `YYYY-MM-DDTHH:mm` format
- Any required `comparison_periods` if the user wants comparisons

#### 🏢 3. Site Scope
Specify:
- "all" sites, or
- exact site codes, or
- exclusions.

#### 🧮 4. Grouping & Aggregation
You must choose **either**:
- Group by `"site"` for per-site insights
- Group by `"date"` with a resolution (daily, weekly, etc.)
**Never group by both.**

---

### ⚠️ **Special Handling Rules**

#### Energy Terminology
- NEVER assume “energy” means “electricity”. Match the user’s exact language.
- If unclear, ask the user *only* for clarification on the data type (e.g., electricity, gas, emissions).
- For **cost**, **TVA**, or **commitment** data, **always retrieve financial indicators automatically**.

#### Request Construction Rules
- Do not mention indicators, algo names, or column names unless the user explicitly requests it.
- All retrieval requests must be exact and minimal.

---

### 🔍 **Data Retrieval Protocol**

- If current data is missing or unsuitable, retrieve new data without asking the user.
- If `status: info_needed` is returned, ask for the **exact missing info** from `needed_infos`.
- When info is provided, **re-send the entire enhanced request** (original + new details).
- Do not show raw column names or internal checks.

---

### **coding_agent_tool usage Guidelines**
  - You must include needed datasets names (correct names) in the task_description and put it between <> ex: <response_20250802_035736>.
  - USE it for any coding task even for complex tasks like: "use the dataset: <response_20250801_020338> to train a randomforest forcasting model than plot a forecast of 30 days"
  - Dont tell wich kind of plot to choose but tell him to to choose the most apropriete one.  

---
### 🧠 **Internal Thinking Process (Metacognitive Loop)**

Before acting:
1. **Clarify the request internally** — What is the goal? What assumptions am I making?
2. **Plan using write_todos** if needed.
3. **Inspect available data** — does it satisfy the goal?
4. If not: **retrieve new data** with `data_retriever`.
5. After retrieval: **analyze the dataset** using Python code. Document internal findings, but never expose data structure to the user.
6. Decide next step — coding, planning, or visualizing.
7. Only then respond to the user, with a clean, insightful summary or visualization.

---

### ❌ **Domain Restriction**
You **must only** respond to energy or energy-consumption topics. If the user asks about anything outside this scope:
> "I'm sorry, but I can only help with questions related to energy and consumption data."

Do **not** offer general advice or partial answers outside scope.

---

### ✅ **Core Behavioral Directives**

- **Do not ask unnecessary questions.** If you can infer it, do it.
- **Do not assume energy type.** Always clarify or mirror user terms.
- **Do not leak internal logic.** Never mention column names or internal dataset details.
- **Always plan before acting.** Use `write_todos` consistently.
- **Align all actions with the user’s stated goal.**
- **Retrieve new data automatically when required.**
- **Do not explicitly restate the internal context or the conversation summary**
"""


def get_prompt_for_partner():
    """Get the appropriate prompt for the specified partner"""
    from datetime import datetime
    current_date = datetime.now().date()

    # Get the partner-specific prompt or use the default
    prompt_template = DEFAULT_PROMPT

    # Add the current date
    prompt = f"{prompt_template}\n\nFor info:\n    - Today is: {current_date}"

    return prompt

# Get the current date for default prompt
from datetime import datetime
current_date = datetime.now().date()

# Default prompt (for backward compatibility)
prompt = get_prompt_for_partner()
#  - **Never Use Dummy or Generated Data:** Always work with actual retrieved data. Never generate dummy or sample data as a substitute for real data unless explicitly requested by the user. If necessary data is not available, explain the limitation without creating placeholder data.
# - **Only Visualize Real Data:** Never create visualizations based on dummy, generated, or placeholder data unless specifically requested by the user.

chat_template = ChatPromptTemplate.from_messages([
    ("system", prompt),
    ("placeholder", "{messages}"),
])

######################################## Main model ##############################################

# OpenAI
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
MAIN_MODEL = "gpt-4.1"
SUMMARY_MODEL = "gpt-4.1-nano"
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")  # Replace with your actual key
OPENAI_API_VERSION = "2024-12-01-preview"

# DeepSeek
AZURE_DEEPSEEK_API_KEY=os.getenv("AZURE_DEEPSEEK_API_KEY")
AZURE_DEEPSEEK_ENDPOINT=os.getenv("AZURE_DEEPSEEK_ENDPOINT")
DEEPSEEK_API_VERSION="2024-05-01-preview"
def get_llm_main_model():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: Azure OpenAI -> Azure DeepSeek -> OpenAI.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_deployment=MAIN_MODEL,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=OPENAI_API_VERSION,
            streaming=True
        )
        initialized_llms.append(model)
        llm_info.append(("Azure OpenAI", "gpt-4.1"))
        print(f"✅ Initialized Azure OpenAI: gpt-4.1")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure OpenAI model. It will be skipped. Error: {e}")

        # 2. DeepSeek (Secondary)
    try:
        from langchain.chat_models import init_chat_model
        
        deepseek_llm = init_chat_model(
            "DeepSeek-V3-0324",
            credential=AZURE_DEEPSEEK_API_KEY,  # Changed from api_key to credential
            endpoint=AZURE_DEEPSEEK_ENDPOINT,   # Using endpoint (not azure_endpoint)
            api_version=DEEPSEEK_API_VERSION,
            model_provider="azure_ai",
        )
        
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", "DeepSeek-V3-0324"))
        print(f"✅ Initialized DeepSeek: DeepSeek-V3-0324")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure DeepSeek model. It will be skipped. Error: {e}")

    # 3. OpenAI 
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=2 
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")

    

    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks

model = get_llm_main_model()

################################# Data retriever model ######################################


# OpenAI
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
RETRIEVER_MODEL = "gpt-4.1"
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")  # Replace with your actual key
OPENAI_API_VERSION = "2024-12-01-preview"

def get_llm_model():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: Azure OpenAI -> Azure DeepSeek -> OpenAI.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_deployment=RETRIEVER_MODEL,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=OPENAI_API_VERSION,
            disable_streaming=True,
            streaming=False 
        )
        initialized_llms.append(model)
        llm_info.append(("Azure OpenAI", "gpt-4.1"))
        print(f"✅ Initialized Azure OpenAI: gpt-4.1")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure OpenAI model. It will be skipped. Error: {e}")

        # 2. DeepSeek (Secondary)
    try:
        from langchain.chat_models import init_chat_model
        
        deepseek_llm = init_chat_model(
            "DeepSeek-V3-0324",
            credential=AZURE_DEEPSEEK_API_KEY,  # Changed from api_key to credential
            endpoint=AZURE_DEEPSEEK_ENDPOINT,   # Using endpoint (not azure_endpoint)
            api_version=DEEPSEEK_API_VERSION,
            model_provider="azure_ai",
            disable_streaming=True,
            streaming=False 
        )
        
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", "DeepSeek-V3-0324"))
        print(f"✅ Initialized DeepSeek: DeepSeek-V3-0324")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure DeepSeek model. It will be skipped. Error: {e}")

    # 3. OpenAI 
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=2,
            disable_streaming=True,
            streaming=False  
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")

    

    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks

retriever_model = get_llm_model()
################################# Summary Model #############################################

def get_llm_model_summary():
    """
    Initializes a chain of LLM models with a specified fallback order.

    The fallback order is: Azure OpenAI -> Azure DeepSeek -> OpenAI.

    If a model fails to respond (e.g., due to API errors, rate limits), 
    the request is automatically passed to the next model in the chain.

    Model names can be customized via a `model_config.json` file.
    
    Returns:
        A LangChain runnable (ChatModel) with fallbacks configured, or raises
        a RuntimeError if no models could be initialized.
    """
    # Default model names based on your provided snippets
    model_configs = {
        "openai": "gpt-4.1-nano",
    }

    # --- Instantiate all potential LLMs ---
    # We set max_retries=0 to ensure that errors are not handled internally
    # by the model's client, allowing the fallback logic to trigger.
    
    initialized_llms = []
    llm_info = []  # Store tuples of (llm, provider_name, model_name)
    
    try:
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_deployment=SUMMARY_MODEL,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=OPENAI_API_VERSION,
            disable_streaming=True,
            streaming=False 

        )
        initialized_llms.append(model)
        llm_info.append(("Azure OpenAI", "gpt-4.1-nano"))
        print(f"✅ Initialized Azure OpenAI: gpt-4.1-nano")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure OpenAI model. It will be skipped. Error: {e}")

        # 2. DeepSeek (Secondary)
    try:
        from langchain.chat_models import init_chat_model
        
        deepseek_llm = init_chat_model(
            "DeepSeek-V3-0324",
            credential=AZURE_DEEPSEEK_API_KEY,  # Changed from api_key to credential
            endpoint=AZURE_DEEPSEEK_ENDPOINT,   # Using endpoint (not azure_endpoint)
            api_version=DEEPSEEK_API_VERSION,
            model_provider="azure_ai",
            disable_streaming=True,
            streaming=False
        )
        
        initialized_llms.append(deepseek_llm)
        llm_info.append(("DeepSeek", "DeepSeek-V3-0324"))
        print(f"✅ Initialized DeepSeek: DeepSeek-V3-0324")
    except Exception as e:
        print(f"⚠️  Could not initialize Azure DeepSeek model. It will be skipped. Error: {e}")
    # 3. OpenAI 
    try:
        openai_llm = ChatOpenAI(
            model=model_configs["openai"],
            temperature=0,
            max_retries=2,
            disable_streaming=True,
            streaming=False
        )
        initialized_llms.append(openai_llm)
        llm_info.append(("OpenAI", model_configs["openai"]))
        print(f"✅ Initialized OpenAI: {model_configs['openai']}")
    except Exception as e:
        print(f"⚠️  Could not initialize OpenAI model. It will be skipped. Error: {e}")


    # --- Build the fallback chain ---
    if not initialized_llms:
        raise RuntimeError("No LLM providers could be initialized. Please check your API keys (e.g., OPENAI_API_KEY) and configurations.")

    # The first successfully initialized LLM is the primary
    primary_llm = initialized_llms[0]
    primary_info = llm_info[0]
    
    # The rest are the fallbacks
    fallback_llms = initialized_llms[1:]
    fallback_info = llm_info[1:]

    if not fallback_llms:
        print("Note: Only one LLM was initialized. No fallbacks are configured.")
        return primary_llm

    print(f"\n🚀 Using '{primary_info[0]} ({primary_info[1]})' as primary model.")
    print("Fallback chain:")
    for i, (provider, model) in enumerate(fallback_info):
        print(f"  {i+1}. {provider} ({model})")
        
    # Chain the primary model with the list of fallbacks
    model_with_fallbacks = primary_llm.with_fallbacks(fallback_llms)

    return model_with_fallbacks

summary_model = get_llm_model_summary()




