[[], {"partner.code": "abcd", "widget.chart_type": "scatter_plot", "start_date": "2024-08-01T00:00", "end_date": "2025-08-01T00:00", "perimeter.type": "site", "perimeter.list": ["all"], "comparison_periods": ["current"], "indicators": [{"code": "consumption_meter_supply-GAS", "formula": "{{consumption_meter_supply#out:consumption_meter_supply-GAS}}"}], "group_by": "date", "group_id": ["monthly"], "time_resolution": "monthly", "data_description": "Monthly gas consumption analysis for all sites from 2024-08-01 to 2025-08-01, grouped by month using the consumption_meter_supply algorithm with indicator label consumption_meter_supply-GAS."}, [[], {"partner.code": "abcd", "widget.chart_type": "scatter_plot", "start_date": "2024-08-01T00:00", "end_date": "2025-08-01T00:00", "perimeter.type": "site", "perimeter.list": ["all"], "comparison_periods": ["current"], "indicators": [{"code": "consumption_meter_supply-GAS", "formula": "{{consumption_meter_supply#out:consumption_meter_supply-GAS}}"}], "group_by": "date", "group_id": ["monthly"], "time_resolution": "monthly", "data_description": "Monthly gas consumption analysis for all sites from 2024-08-01 to 2025-08-01, grouped by month using the consumption_meter_supply algorithm with indicator label consumption_meter_supply-GAS."}], [], [[], {"partner.code": "abcd", "widget.chart_type": "scatter_plot", "start_date": "2024-08-01T00:00", "end_date": "2025-08-01T00:00", "perimeter.type": "site", "perimeter.list": ["all"], "comparison_periods": ["current"], "indicators": [{"code": "consumption_meter_supply-GAS", "formula": "{{consumption_meter_supply#out:consumption_meter_supply-GAS}}"}], "group_by": "date", "group_id": ["monthly"], "time_resolution": "monthly", "data_description": "Monthly gas consumption analysis for all sites from 2024-08-01 to 2025-08-01, grouped by month using the consumption_meter_supply algorithm with indicator label consumption_meter_supply-GAS."}, [[], {"partner.code": "abcd", "widget.chart_type": "scatter_plot", "start_date": "2024-08-01T00:00", "end_date": "2025-08-01T00:00", "perimeter.type": "site", "perimeter.list": ["all"], "comparison_periods": ["current"], "indicators": [{"code": "consumption_meter_supply-GAS", "formula": "{{consumption_meter_supply#out:consumption_meter_supply-GAS}}"}], "group_by": "date", "group_id": ["monthly"], "time_resolution": "monthly", "data_description": "Monthly gas consumption analysis for all sites from 2024-08-01 to 2025-08-01, grouped by month using the consumption_meter_supply algorithm with indicator label consumption_meter_supply-GAS."}], []], {"partner.code": "abcd", "widget.chart_type": "scatter_plot", "start_date": "2024-08-01T00:00", "end_date": "2025-08-01T00:00", "perimeter.type": "site", "perimeter.list": ["all"], "comparison_periods": ["current"], "indicators": [{"code": "consumption_meter_supply-GAS", "formula": "{{consumption_meter_supply#out:consumption_meter_supply-GAS}}"}], "group_by": "date", "group_id": ["monthly"], "time_resolution": "monthly", "data_description": "Monthly gas consumption analysis for all sites from 2024-08-01 to 2025-08-01, grouped by month using the consumption_meter_supply algorithm with indicator label consumption_meter_supply-GAS."}]