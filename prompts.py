WRITE_TODOS_DESCRIPTION = """Use this tool to create and manage a structured task list for your current energy analysis work session. This helps you track progress, organize complex analytical workflows, and demonstrate thoroughness to the user.
It also helps the user understand the progress of their energy analysis requests and overall project status.
When to Use This Tool
Use this tool proactively in these scenarios:
Complex energy analysis workflows - When your analysis requires 3 or more distinct steps (e.g., data collection → preprocessing → modeling → validation → reporting)
Multi-source data integration - When combining data from energy APIs, weather services, market databases, and sensor networks
User explicitly requests a todo list - When the user directly asks you to create an analysis roadmap
Multiple energy tasks - When users provide several analysis requests (e.g., "analyze load profiles, forecast demand, and evaluate solar potential")
After receiving new energy data or requirements - Immediately capture new analysis requirements as todos
When starting analysis work - Mark tasks as in_progress BEFORE beginning work. Only have ONE task in_progress at a time
After completing analysis steps - Mark tasks as completed and add any new follow-up tasks discovered during analysis
When NOT to Use This Tool
Skip using this tool when:
Single, straightforward query (e.g., "What's the current electricity price in Texas?")
Trivial data lookup with no analysis required
Simple one-step calculation (e.g., converting kWh to MWh)
Pure informational questions about energy concepts
Energy Analysis Examples
<example>
User: I need to analyze the renewable energy potential for a commercial building in Denver, including solar irradiance data, wind patterns, and economic feasibility.
Assistant: I'll help you conduct a comprehensive renewable energy analysis for your Denver commercial building. Let me create a todo list to systematically evaluate all renewable options.
*Creates todo list with the following items:*
1. Retrieve historical solar irradiance data for Denver coordinates using weather API
2. Analyze wind speed and direction patterns from NOAA data
3. Calculate solar panel output potential using PVWatts model
4. Assess wind turbine feasibility based on local zoning and wind resources
5. Gather utility electricity rates and net metering policies for ROI calculations
6. Create financial model including installation costs, incentives, and payback period
7. Generate visualization comparing solar vs wind vs hybrid scenarios
8. Compile final recommendation report with sensitivity analysis
*Begins working on solar irradiance data retrieval*
<reasoning>
The assistant used the todo list because:
1. Renewable energy analysis involves multiple complex data sources and calculations
2. Each energy type requires different datasets and modeling approaches
3. Financial analysis adds another layer of complexity requiring utility data and incentives
4. The systematic approach ensures comprehensive coverage of all renewable options
</reasoning>
</example>
<example>
User: Analyze the load profile for this manufacturing facility and identify peak demand patterns to optimize their energy costs.
Assistant: I'll conduct a detailed load profile analysis to identify peak demand optimization opportunities. Let me create a todo list for this energy analysis.
*Creates todo list with the following items:*
1. Import facility energy consumption data from provided files/API
2. Clean and validate load data using pandas, handling missing values and outliers
3. Calculate hourly, daily, and monthly load factors
4. Identify peak demand periods and duration using statistical analysis
5. Correlate load patterns with production schedules and weather data
6. Analyze demand charges from utility tariff structure
7. Calculate potential savings from peak shaving strategies
8. Create interactive dashboard showing load profiles and optimization scenarios
9. Provide specific recommendations for demand response programs
*Begins data import and validation*
<reasoning>
The assistant used the todo list because:
1. Load profile analysis requires multiple analytical steps from data processing to insights
2. Peak demand optimization involves technical analysis and financial modeling
3. The workflow benefits from systematic progress tracking for complex energy data
</reasoning>
</example>
<example>
User: Help me forecast next quarter's electricity demand for our utility service territory using weather forecasts and historical consumption patterns.
Assistant: I'll create a comprehensive electricity demand forecast for your utility. Let me structure this multi-step forecasting analysis.
*Creates todo list with the following items:*
1. Retrieve historical electricity consumption data by customer class (residential, commercial, industrial)
2. Collect weather forecast data for next quarter (temperature, humidity, cooling degree days)
3. Perform exploratory data analysis to identify seasonal patterns and trends
4. Build regression model correlating weather variables with electricity demand
5. Validate model accuracy using cross-validation techniques
6. Generate forecast scenarios (base case, hot summer, mild summer)
7. Calculate forecast uncertainty intervals and confidence levels
8. Create executive summary with key drivers and risk factors
9. Provide recommendations for generation planning and demand response programs
*Begins historical data retrieval*
<reasoning>
The assistant used the todo list because:
1. Demand forecasting involves multiple data sources and modeling techniques
2. Weather integration adds complexity requiring specialized data handling
3. Utility-scale analysis requires validation and uncertainty quantification
4. The systematic approach ensures robust and defensible forecasts
</reasoning>
</example>
<example>
User: What's the current natural gas price at Henry Hub?
Assistant: The current Henry Hub natural gas price is $2.85/MMBtu, based on the latest NYMEX futures data.
<reasoning>
The assistant did not use the todo list because this is a single, straightforward price lookup requiring no complex analysis.
</reasoning>
</example>
<example>
User: Convert 500 kWh to MWh
Assistant: 500 kWh equals 0.5 MWh (simply divide by 1,000).
<reasoning>
The assistant did not use the todo list because this is a simple unit conversion that can be completed in one step.
</reasoning>
</example>
Task States and Management
Task States: Use these states to track progress:
pending: Analysis step not yet started
in_progress: Currently analyzing (limit to ONE task at a time)
completed: Analysis step finished successfully
Task Management:
Update task status in real-time as you work
Mark tasks complete IMMEDIATELY after finishing (don't batch completions)
Only have ONE task in_progress at any time
Complete current analysis before starting new tasks
Remove tasks that are no longer relevant from the list entirely
Task Completion Requirements:
ONLY mark a task as completed when you have FULLY accomplished the analysis
If you encounter data errors, API failures, or cannot complete analysis, keep the task as in_progress
When blocked, create a new task describing what needs to be resolved (e.g., "Resolve data quality issues in load profile")
Never mark a task as completed if:
Data validation reveals significant issues
Analysis results are incomplete or questionable
Required data sources are unavailable
Quality standards for energy analysis haven't been met
Task Breakdown:
Create specific, actionable items (e.g., "Calculate capacity factor for wind turbine" not "Analyze wind")
Break complex energy analyses into logical steps
Use clear, descriptive task names that indicate the specific analysis being performed
When in doubt, use this tool. Being proactive with task management demonstrates professional energy analysis practices and ensures comprehensive coverage of all requirements."""
