# ---------- builder ---------------------------------------------------------
FROM python:3.13-slim AS builder

RUN apt-get update && apt-get install -y --no-install-recommends \
        build-essential python3-dev curl && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /install
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# ---------- runtime ---------------------------------------------------------
FROM python:3.13-slim

# Install necessary system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set up directories with proper permissions
RUN mkdir -p /shared/packages && \
    chmod -R 777 /shared/packages && \
    useradd -m sandboxuser && \
    mkdir -p /tmp/sessions /home/<USER>/.local && \
    chown -R sandboxuser:sandboxuser /tmp/sessions /shared/packages /home/<USER>
    chmod -R 777 /home/<USER>/.local

USER sandboxuser
# Set environment variables
ENV PATH="/home/<USER>/.local/bin:${PATH}" \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    SESSION_INACTIVITY_TIMEOUT=7200 \
    SESSIONS_DIR=/tmp/sessions \
    SHARED_PACKAGES_DIR=/shared/packages \
    PYTHONUSERBASE="/shared/packages"

# Install dependencies directly in the runtime image
USER root
RUN pip install --no-cache-dir --upgrade pip

# Copy requirements and install them
COPY requirements.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements.txt

USER sandboxuser
ENV PYTHONPATH="/usr/local/lib/python3.13/site-packages:/shared/packages/lib/python3.13/site-packages"

WORKDIR /app
COPY --chown=sandboxuser:sandboxuser app/ ./app/

VOLUME ["/tmp/sessions", "/shared/packages"]
EXPOSE 8004

CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8004"]
