#!/usr/bin/env python3
"""
client_api_test.py

A comprehensive test suite for the Python Sandbox API, covering:

1. Session management (create, isolation, cleanup)
2. Code execution (basic, error handling, timeouts)
3. Variable persistence and state management
4. Plot generation and deduplication (<PERSON>lot<PERSON>, Matplotlib)
5. File operations (upload, download, CSV handling)
6. Library installation and management
7. Error scenarios and edge cases
8. Performance and resource monitoring
9. Concurrent session testing
10. Data format validation
"""

import requests
import json
import sys
import time
import subprocess
import pickle
import pandas as pd
import io
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Set, Optional, Any
import tempfile
import os

API_BASE = "http://localhost:8004"
CONTAINER_PATTERN = "python-sandbox-api"

# Test configuration
TEST_CONFIG = {
    "timeout_short": 5,
    "timeout_medium": 15,
    "timeout_long": 30,
    "concurrent_sessions": 3,
    "stress_test_iterations": 5
}


def pretty_print(data):
    print(json.dumps(data, indent=2, default=str))


def get_matching_containers(name_pattern=CONTAINER_PATTERN):
    try:
        output = subprocess.check_output(
            ["docker", "ps", "--format", "{{.Names}}"],
            text=True
        )
        return [name for name in output.splitlines() if name_pattern in name]
    except Exception:
        return []


def print_container_ports(container_name):
    try:
        mapping = subprocess.check_output(
            ["docker", "port", container_name],
            text=True
        )
        print(f"Port mappings for {container_name}:\n{mapping.strip()}")
    except Exception as e:
        print(f"Could not get port mappings for {container_name}: {e}")


def wait_for_server(timeout=30):
    start = time.time()
    while time.time() - start < timeout:
        try:
            r = requests.get(f"{API_BASE}/sessions")
            if r.status_code in (200, 404):
                print("Server is up and responding.")
                return
        except requests.exceptions.ConnectionError:
            print(f"Connection error: cannot reach API at {API_BASE}")
            containers = get_matching_containers()
            if not containers:
                print("-> No sandbox container found. Please start with 'docker-compose up'.")
                sys.exit(1)
            print(f"Found containers: {containers}")
            for cname in containers:
                print_container_ports(cname)
        except Exception as e:
            print(f"Request error: {e}")
        print("Waiting for server to start...")
        time.sleep(1)
    print(f"Server not available after {timeout} seconds.")
    sys.exit(1)


def execute_and_handle_plots(session_id, code, timeout=30, downloaded_plots=None):
    if downloaded_plots is None:
        downloaded_plots = set()

    payload = {"code": code, "timeout_seconds": timeout}
    print(f"\n[DEBUG PLOT] Executing code in session {session_id}")
    resp = requests.post(f"{API_BASE}/sessions/{session_id}/execute", json=payload)
    resp.raise_for_status()
    result = resp.json()

    print("\n--- Execution Output ---")
    print(result.get("output", ""))

    plots = result.get("plots", []) or []
    print(f"\n[DEBUG PLOT] Plots returned from sandbox: {plots}")
    
    new_plots = []
    for name in plots:
        if name not in downloaded_plots:
            # Only download plots we haven't seen before
            url = f"{API_BASE}/sessions/{session_id}/plots/{name}"  # Changed from /files/ to /plots/
            print(f"[DEBUG PLOT] Downloading plot: {name} from {url}")
            dl = requests.get(url)
            dl.raise_for_status()
            print(f"[DEBUG PLOT] Successfully downloaded plot: {name} ({len(dl.content)} bytes)")
            with open(name, 'wb') as f:
                f.write(dl.content)
            print(f"[DEBUG PLOT] Saved plot file: {name}")
            downloaded_plots.add(name)
            new_plots.append(name)
        else:
            print(f"[DEBUG PLOT] Skipping already downloaded plot: {name}")

    # Replace the plots list with only the new plots
    result["plots"] = new_plots
    print(f"\n[DEBUG PLOT] Final plots list: {new_plots}")
    return result


def main():
    wait_for_server()

    # Track downloaded plots to avoid duplicates
    downloaded_plots = set()

    # 1) Session isolation test
    print("\n=== Testing session isolation ===")
    sid1 = requests.post(f"{API_BASE}/sessions").json()['session_id']
    sid2 = requests.post(f"{API_BASE}/sessions").json()['session_id']
    print(f"Created sessions: {sid1}, {sid2}")

    # Define x in session1
    res1 = execute_and_handle_plots(sid1, 'x = 42\nprint(f"x set to {x}")', downloaded_plots=downloaded_plots)
    # Try to read x in session2
    res2 = execute_and_handle_plots(sid2, 'print(x)', downloaded_plots=downloaded_plots)
    if 'NameError' in res2.get('output', ''):
        print("[PASS] session isolation confirmed: x not in session2")
    else:
        print("[FAIL] session2 unexpectedly has x")

    # Continue tests in session1
    session_plots = []
    print("\n=== Executing code to define variable 'y' ===")
    res = execute_and_handle_plots(sid1, 'y = 123\nprint(f"Variable y set to {y}")', downloaded_plots=downloaded_plots)
    session_plots.extend(res.get('plots', []))

    print("\n=== Verifying variable persistence ===")
    vars_info = requests.get(f"{API_BASE}/sessions/{sid1}").json().get('variables', {})
    pretty_print(vars_info)
    print(f"[{'PASS' if 'y' in vars_info else 'FAIL'}] Variable y {'found' if 'y' in vars_info else 'not found'} in session1")

    print("\n=== Using persistent variable 'y' in execution ===")
    res = execute_and_handle_plots(sid1, 'print(f"y*2={y*2}")', downloaded_plots=downloaded_plots)
    session_plots.extend(res.get('plots', []))

    # 3) Plotly figure
    print("\n=== Generating Plotly figure ===")
    plotly_code = (
        "import plotly.graph_objects as go\n"
        "fig = go.Figure(data=go.Bar(x=[1,2,3], y=[3,1,2]))\n"
        "print('Created Plotly bar figure')"
    )
    res = execute_and_handle_plots(sid1, plotly_code, downloaded_plots=downloaded_plots)
    session_plots.extend(res.get('plots', []))


    print("\n=== Downloading CSV ===")
    dl = requests.get(f"{API_BASE}/sessions/{sid1}/files/data.csv")
    dl.raise_for_status()
    print(dl.text)

    # 6) Seaborn install/test
    print("\n=== Installing seaborn ===")
    lib = requests.post(
        f"{API_BASE}/libraries/install",  # Use global install endpoint
        json={'libraries': ['seaborn']}
    )
    lib.raise_for_status()
    pretty_print(lib.json())

    print("\n=== Testing seaborn import ===")
    sns_res = execute_and_handle_plots(sid1, 'import seaborn as sns\nprint(sns.__version__)', downloaded_plots=downloaded_plots)
    session_plots.extend(sns_res.get('plots', []))

    # 7) List sessions
    print("\n=== Listing sessions ===")
    ls = requests.get(f"{API_BASE}/sessions")
    pretty_print(ls.json())

    # 8) Delete session1
    print("\n=== Deleting session1 ===")
    de = requests.delete(f"{API_BASE}/sessions/{sid1}")
    pretty_print(de.json())
    print(f"[{'PASS' if de.json().get('success') else 'FAIL'}] Deleted session1 {sid1}")

    # 9) Verify deletion & cleanup
    print("\n=== Verifying session1 deletion ===")
    chk = requests.get(f"{API_BASE}/sessions/{sid1}")
    print(f"[{'PASS' if chk.status_code == 404 else 'FAIL'}] session1 deletion returned {chk.status_code}")

    print("\n=== Verifying plot cleanup ===")
    cleaned, remaining = [], []
    for name in session_plots:
        url = f"{API_BASE}/sessions/{sid1}/files/{name}"
        r = requests.get(url)
        if r.status_code == 404:
            cleaned.append(name)
            print(f"[PASS] {name} cleaned up")
        else:
            remaining.append(name)
            print(f"[FAIL] {name} still exists ({r.status_code})")
    print(f"Summary: cleaned={len(cleaned)}, remaining={len(remaining)}")


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)