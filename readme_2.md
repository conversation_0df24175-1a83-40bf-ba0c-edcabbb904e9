### 🔹 **Semantic Memory** (Facts & Knowledge)

> Stores key facts or structured knowledge, often about the user, their data, or persistent preferences.

#### ✅ Use cases for your energy agent:

* Storing user-specific configurations, site hierarchies, preferred units or formats.
* Remembering which meters or sensors belong to which sites.
* Remembering known anomalies or operational changes.

#### 🧠 Examples:

```python
ExtractedMemory(
    id="site-relationship-01",
    content=Memory(
        content="The 'Site Toulouse-West' includes 3 buildings and has solar panels installed since March 2024."
    )
)

ExtractedMemory(
    id="user-preference-01",
    content=Memory(
        content="Zakaria prefers to see consumption trends in kWh and compare them monthly against the same month last year."
    )
)

ExtractedMemory(
    id="data-note-01",
    content=Memory(
        content="The 'Lyon Office' experienced a major HVAC failure from July 10 to July 15, 2025, affecting energy consumption."
    )
)
```

---

### 🔹 **Procedural Memory** (Behavioral Rules & Adaptations)

> Encodes how the agent should behave — what tone to use, how to handle certain types of requests, etc. It can evolve based on observed behavior patterns.

#### ✅ Use cases for your energy agent:

* Adapting how the agent explains anomalies or trends (technical vs layman).
* Choosing whether to show charts first or summarize key stats first.
* Learning to include cost estimates when certain users ask for comparisons.

#### 🧠 Example prompt update:

```text
If the user asks to compare consumption, always:
  - Include both absolute and relative difference.
  - Mention seasonality if comparing different months.
  - Highlight cost differences if energy price is available.

When the user refers to "last summer," resolve that to June-August of the most recent full year.
```

> This could be updated dynamically over time as new habits are detected.

---

### 🔹 **Episodic Memory** (Past Interactions & Examples)

> Stores specific examples of past interactions. Useful for generating few-shot prompts, remembering the flow of long-running analysis, or reminding the agent how a problem was solved.

#### ✅ Use cases for your energy agent:

* Recalling how the user previously defined a baseline for comparison.
* Re-using a previously built filtering condition (e.g., exclude weekends).
* Providing continuity for ongoing analysis across sessions.

#### 🧠 Examples:

```python
# Stored interaction snippet distilled as few-shot example
EpisodicMemory(
    id="baseline-definition-2025",
    content="""
User: I want to compare this year to a normal baseline.
Agent: Should I use the average of the last 3 full years, excluding 2020 due to COVID impact?
User: Yes, that works.
"""
)

EpisodicMemory(
    id="last-comparison-session",
    content="""
In the previous session, the user compared energy consumption of 'Site A' in March 2025 vs March 2024 and found a 12% increase mainly due to extended HVAC usage.
"""
)
```

---




