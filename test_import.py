#!/usr/bin/env python3

import sys
import traceback
import importlib.util

def test_parser_graph_import():
    """Test importing parser_graph module step by step"""
    
    print("Testing parser_graph import...")
    
    try:
        # Method 1: Standard import
        print("Method 1: Standard import")
        import parser_graph
        print("✓ Module imported successfully")
        
        # Check if classes are available
        has_error_classifier = hasattr(parser_graph, 'ErrorClassifier')
        has_retry_manager = hasattr(parser_graph, 'retry_manager')
        
        print(f"✓ ErrorClassifier available: {has_error_classifier}")
        print(f"✓ retry_manager available: {has_retry_manager}")
        
        if not has_error_classifier:
            print("✗ ErrorClassifier is missing from module")
            
        if not has_retry_manager:
            print("✗ retry_manager is missing from module")
            
        # Show what's actually available
        available_attrs = [attr for attr in dir(parser_graph) if not attr.startswith('_')]
        print(f"Available attributes: {len(available_attrs)}")
        for attr in sorted(available_attrs):
            print(f"  - {attr}")
            
        return has_error_classifier and has_retry_manager
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_direct_access():
    """Test accessing the classes directly"""
    print("\nTesting direct access...")
    
    try:
        from parser_graph import ErrorClassifier, retry_manager
        print("✓ Direct import successful")
        print(f"✓ ErrorClassifier: {ErrorClassifier}")
        print(f"✓ retry_manager: {retry_manager}")
        return True
    except ImportError as e:
        print(f"✗ Direct import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_parser_graph_import()
    success2 = test_direct_access()
    
    if success1 and success2:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)
