# tools.py

import os
import sys
import uuid
import pickle
import json
import traceback
from io import String<PERSON>
from datetime import datetime
from typing import Annotated, Optional, Dict, Any, List

import pandas as pd

from langchain_core.tools import tool
from langchain_core.tools.base import InjectedToolCallId
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import ToolMessage

from parser_graph import run_generic_parser_graph

from sandbox_client import SandboxClient
from langgraph.types import Command
import logging
import time
import random

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tools")
logger.setLevel(logging.INFO)
# Centralized file logger for investigation
import logging.handlers as _lh
_LOG_FILE_PATH = "logs/session_debug.log"
os.makedirs(os.path.dirname(_LOG_FILE_PATH), exist_ok=True)
_file_handler = _lh.RotatingFileHandler(_LOG_FILE_PATH, maxBytes=2_000_000, backupCount=3)
_file_handler.setFormatter(logging.Formatter("%(asctime)s %(levelname)s %(name)s - %(message)s"))
if not any(isinstance(h, _lh.RotatingFileHandler) for h in logger.handlers):
    logger.addHandler(_file_handler)
# Ensure project root is on sys.path for reliable package imports
try:
    import sys as _sys, os as _os
    _proj_root = _os.path.abspath(_os.path.dirname(__file__))
    if _proj_root not in _sys.path:
        _sys.path.insert(0, _proj_root)
except Exception:
    pass
# Import coding agent runner (raise on failure)
from agents.coding_agent import run_coding_agent

# Data retriever tool
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from typing import Annotated
from langchain_core.tools import InjectedToolCallId

@tool("data_retriever")
def data_retriever(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: RunnableConfig,
    description: Optional[str] = None,
    dataset_name: Optional[str] = None
) -> dict:
    '''
    Retrieves data based on a description or dataset name by querying an external source.

    :param tool_call_id: Injected tool call ID
    :param config: Runnable configuration (expects configurable.session_id and configurable.partner)
    :param description: Description of the data to retrieve
    :param dataset_name: Name of the dataset to retrieve
    '''
    # 1) Read config (session_id is mandatory)
    session_id = config.get("configurable", {}).get("session_id")
    partner = config.get("configurable", {}).get("partner", "oksigen")

    if not session_id:
        raise ValueError("Session ID is required in config for data_retriever")

    # 2) Pick query
    query = description or dataset_name
    if not query:
        error_message = "Data retrieval failed: Neither description nor dataset_name provided"
        print(f"[Tool][DataRetriever] Error: {error_message}")
        return {
            "status": "error",
            "error": error_message
        }

    print(f"[Tool][DataRetriever] Trying query='{query}' partner='{partner}' session='{session_id}'")

    try:
        # 3) Run the graph (returns final STATE dict)
        state = run_generic_parser_graph(query, session_id=session_id, partner=partner)
        # Expected keys in state: user_request, generated_request (processed), raw_generated_request (raw), 
        # response_data, requests_error, input_data, needed_infos, ...

        print(f"[Tool][DataRetriever] Graph finished. Keys={list(state.keys())}")

        needed_infos = state.get("needed_infos") or ""
        # Use raw_generated_request instead of generated_request for the original LLM output
        raw_generated_requests = state.get("raw_generated_request", [])  # RAW LLM requests (before site/partner augmentation)
        processed_generated_requests = state.get("generated_request", [])  # Processed requests (after site/partner augmentation)
        requests_error = state.get("requests_error", []) or []
        input_data_objs = state.get("input_data", []) or []

        # 3.a) If the retriever needs more info, bubble that up
        if needed_infos:
            return {
                "status": "info_needed",
                "needed_infos": needed_infos,
                "input_data": [],
                "data_description": [query],
                "generic_parser_request": raw_generated_requests,  # raw attempts before processing
                "processed_generic_parser_request": processed_generated_requests,  # processed versions
                "requests_error": requests_error
            }

        # 3.b) Success: input_data present (already filtered to non-empty by your guarded pipeline)
        if input_data_objs:
            # Convert InputData objects from the graph state
            input_data_serialized = []
            for d in input_data_objs:
                if hasattr(d, 'variable_name'):  # It's an InputData object
                    input_data_serialized.append({
                        "variable_name": d.variable_name,
                        "data_path": d.sandbox_file_name,
                        "data_description": d.data_description,
                        "sandbox_file_name": d.sandbox_file_name,
                    })
                else:  # It's a dict (fallback)
                    input_data_serialized.append({
                        "variable_name": d.get("variable_name", ""),
                        "data_path": d.get("sandbox_file_name", ""),
                        "data_description": d.get("data_description", query),
                        "sandbox_file_name": d.get("sandbox_file_name", ""),
                    })
            
            return {
                "status": "success",
                "input_data": input_data_serialized,
                "data_description": [query],
                "generic_parser_request": raw_generated_requests,  # raw LLM JSON(s) before processing
                "processed_generic_parser_request": processed_generated_requests,  # processed versions
                "requests_error": requests_error
            }

        # 3.c) No data; if we have error codes, surface them clearly
        if requests_error:
            recent_error = requests_error[-1]  # most recent error
            
            # Classify error types based on the enhanced error handling
            error_classification = "unknown"
            if "CONNECTION_ERROR" in recent_error:
                error_classification = "connection_error"
            elif "DATA_ERROR" in recent_error:
                error_classification = "data_error"
            elif "EMPTY_DETAILS" in recent_error:
                error_classification = "empty_data"
            elif "INVALID_REQUEST" in recent_error:
                error_classification = "invalid_request"
            elif "MANDATORY_FIELD" in recent_error:
                error_classification = "validation_error"
            elif "SERVER_ERROR" in recent_error:
                error_classification = "server_error"
            elif "UNEXPECTED_ERROR" in recent_error:
                error_classification = "unexpected_error"
                
            return {
                "status": "error",
                "error_code": recent_error,
                "error_classification": error_classification,
                "requests_error": requests_error,
                "generic_parser_request": raw_generated_requests,  # raw attempts
                "processed_generic_parser_request": processed_generated_requests,  # processed versions
                "failed_query": query,
                "total_retry_attempts": len(requests_error)
            }

        # 3.d) Fallback: truly unexpected empty result (should not happen with enhanced error handling)
        return {
            "status": "error",
            "error": "UNKNOWN_EMPTY_RESULT",
            "error_classification": "unexpected_error",
            "generic_parser_request": raw_generated_requests,
            "processed_generic_parser_request": processed_generated_requests,
            "failed_query": query
        }

    except (DataRetrievalError, APIConnectionError, EmptyDataError) as e:
        # Handle our custom exceptions
        error_classification = "data_error"
        if isinstance(e, APIConnectionError):
            error_classification = "connection_error"
        elif isinstance(e, EmptyDataError):
            error_classification = "empty_data"
            
        error_message = f"Data retrieval failed: {str(e)}"
        print(f"[Tool][DataRetriever] {type(e).__name__}: {error_message}")
        return {
            "status": "error",
            "error": error_message,
            "error_classification": error_classification,
            "failed_query": query,
            "exception_type": type(e).__name__
        }
        
    except ValueError as e:
        # Handle validation errors
        error_message = f"Validation error: {str(e)}"
        print(f"[Tool][DataRetriever] ValueError: {error_message}")
        return {
            "status": "error",
            "error": error_message,
            "error_classification": "validation_error",
            "failed_query": query,
            "exception_type": "ValueError"
        }
        
    except Exception as e:
        # Handle unexpected exceptions
        error_message = f"Unexpected error in data retrieval: {str(e)}"
        print(f"[Tool][DataRetriever] Unexpected Error: {error_message}")
        print(f"[Tool][DataRetriever] Trace:\n{traceback.format_exc()}")
        return {
            "status": "error",
            "error": error_message,
            "error_classification": "unexpected_error",
            "failed_query": query,
            "exception_type": type(e).__name__
        }

from pathlib import Path

_PLOT_DIR = Path("images/plotly_figures/pickle")
_PLOT_DIR.mkdir(parents=True, exist_ok=True)

def _debug(msg: str):
    """Centralised debug print – logs to console and file."""
    text = f"[Tool] {msg}"
    print(text)
    try:
        logger.info(text)
    except Exception:
        pass

def _extract_io(config: RunnableConfig) -> tuple[list[dict], dict]:
    """Return (input_data, current_variables) extracted from LangGraph config."""
    try:
        # Debug
        print(f"[_extract_io] Config keys: {list(config.keys())}")
        if "configurable" in config:
            print(f"[_extract_io] Configurable keys: {list(config['configurable'].keys())}")
        
        chan = config.get("configurable", {}).get("__pregel_read", {})
        if hasattr(chan, "args") and len(chan.args) > 1:  # functools.partial
            print("[_extract_io] Found partial in __pregel_read")
            chan = chan.args[1].get("channel_values", {})
        if isinstance(chan, dict):
            input_data = chan.get("input_data", [])
            current_vars = chan.get("current_variables", {})
            print(f"[_extract_io] Found input_data: {len(input_data)} items, current_vars: {len(current_vars)} items")
            return input_data, current_vars
    except Exception as exc:
        _debug(f"config parsing failed: {exc}")
    
    print("[_extract_io] Returning empty lists")
    return [], {}

def _load_csv(path: Path) -> pd.DataFrame:
    """Read a CSV with a one-liner that still gives helpful tracebacks."""
    _debug(f"loading {path}")
    return pd.read_csv(path)

def _auto_load_data(input_data: list[dict]) -> dict[str, pd.DataFrame]:
    """Load datasets declared in input_data or auto-discover *.csv files."""
    variables: dict[str, pd.DataFrame] = {}
    declared = [Path(d["data_path"]) for d in input_data if "data_path" in d]
    discovered = declared or list(Path(".").glob("*.csv"))

    for csv in discovered:
        name = csv.stem
        try:
            variables[name] = _load_csv(csv)
        except Exception as exc:
            _debug(f"failed to load {csv}: {exc}")
    return variables

def _save_figs(figures: list) -> list[str]:
    """Pickle Plotly figures and return saved filenames."""
    names = []
    for fig in figures:
        fname = f"{uuid.uuid4()}.pickle"
        with open(_PLOT_DIR / fname, "wb") as f:
            pickle.dump(fig, f)
        names.append(fname)
    return names

# ── coding agent tool ─────────────────────────────────────────────────────────
@tool("coding_agent_tool")
def coding_agent_tool(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: RunnableConfig,
    task_description: str,
) -> Command:
    """
    Run a coding-only agent that generates Python to complete the task using datasets ALREADY LOADED in the sandbox by their variable names.
    The agent is restricted to using complete_python_task only. Returns a Command that updates state.
    """
    # Validate environment
    session_id = config.get("configurable", {}).get("session_id")
    partner = config.get("configurable", {}).get("partner")
    if not session_id:
        raise ValueError("Session ID is required in config for coding_agent_tool")

    logger.info(f"[coding_agent_tool] start session_id={session_id} partner={partner} task='{task_description[:80]}'")
    logger.info(f"[coding_agent_tool] config.configurable keys: {list(config.get('configurable', {}).keys())}")

    # Lazy-import the model instance here to avoid circular imports
    try:
        from model import model as _model_instance  # noqa: WPS433
    except Exception as _e:
        # Fallback: try alternate attribute if project exposes factory/instance differently
        try:
            from model import get_model as _get_model  # type: ignore
            _model_instance = _get_model()
        except Exception:
            raise

    import asyncio

    async def _run():
        # Lazy import run_coding_agent to ensure latest signature and avoid cycles
        from agents.coding_agent import run_coding_agent  # noqa: WPS433
        logger.info("[coding_agent_tool] invoking inner coding agent with same session_id/partner")
        # Write a breadcrumb to file log too
        _debug(f"[coding_agent_tool] start session_id={session_id} partner={partner}")
        result = await run_coding_agent(
            task_description=task_description,
            session_id=session_id,
            partner=partner or "",
            model_instance=_model_instance,
        )
        # Extract outputs
        stdout = result.get("stdout", "")
        plots = result.get("plots", [])
        logger.info(f"[coding_agent_tool] inner result: stdout_len={len(stdout)} plots={len(plots)}")
        _debug(f"[coding_agent_tool] inner result stdout_len={len(stdout)} plots={len(plots)}")

        # Mirror state updates similar to complete_python_task (images, variables via messages already)
        return Command(
            update={
                "output_image_paths": plots,
                "messages": [
                    ToolMessage(
                        content=json.dumps({
                            "stdout": stdout,
                            "plots": plots
                        }),
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )

    # Execute async runner
    try:
        return asyncio.run(_run())
    except Exception as e:
        err = f"coding_agent_tool error: {e}"
        logger.error(f"[coding_agent_tool] exception: {err}\n{traceback.format_exc()}")
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        content=json.dumps({
                            "stdout": err,
                            "plots": []
                        }),
                        tool_call_id=tool_call_id
                    )
                ],
                "output_image_paths": [],
            }
        )

# ── main tool ─────────────────────────────────────────────────────────────────
@tool("complete_python_task")
def complete_python_task(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: RunnableConfig,
    python_code: str,
) -> Command:
    """
    Execute arbitrary user Python code in a sandbox environment.
    Returns a Command to update the state with stdout, image filenames, and enriched current_variables.

    :param tool_call_id: Injected tool call ID
    :param config: Runnable configuration
    :param python_code: Python code to execute
    """
    # Get session_id from config
    cfg = config.get("configurable", {}) or {}
    session_id = cfg.get("session_id")
    logger.info(f"[complete_python_task] received config.configurable={list(cfg.keys())} session_id={session_id}")
    _debug(f"[complete_python_task] received config.configurable={list(cfg.keys())} session_id={session_id}")
    if not session_id:
        raise ValueError("Session ID is required in config for complete_python_task")

    try:
        import asyncio
        from sandbox_client import SandboxClient

        async def execute_in_sandbox():
            client = SandboxClient()

            # Ensure we have a valid session
            session_info = await client.get_session_info(session_id)
            if not session_info:
                logger.warning(f"[complete_python_task] session {session_id} not found; creating it")
                _debug(f"[complete_python_task] session {session_id} not found; creating it")
                # Create a new session if it doesn't exist
                await client.create_session(session_id)
                session_info = await client.get_session_info(session_id)

            # Log the server-echoed session id to detect mismatch
            echoed_id = (session_info or {}).get("session_id")
            logger.info(f"[complete_python_task] server echoed session_id={echoed_id} (requested={session_id})")
            _debug(f"[complete_python_task] server echoed session_id={echoed_id} (requested={session_id})")

            # Debug: Check session variables before executing code
            vars_before = (session_info or {}).get('variables', {})
            _debug(f"[Tool] Session variables before execution KEYS: {list(vars_before.keys()) if isinstance(vars_before, dict) else type(vars_before)}")

            # Debug: List files in the sandbox
            list_files_code = """
import os
print(f"Current directory: {os.getcwd()}")
print(f"Files in current directory: {os.listdir('.')}")
"""
            list_files_result = await client.execute_code(
                session_id=session_id,
                code=list_files_code,
                timeout_seconds=5
            )
            _debug(f"[Tool] Files in sandbox before execution: {list_files_result.get('output', '')}")

            # Execute code in sandbox
            _debug(f"[Tool] Executing code in sandbox session {session_id}")
            sandbox_result = await client.execute_code(
                session_id=session_id,
                code=python_code,
                timeout_seconds=30
            )

            # Debug la sortie complète du sandbox
            print(f"[SANDBOX RESULTS]: {sandbox_result}")

            if isinstance(sandbox_result, dict) and sandbox_result.get("success"):
                # Extract and format variables from the sandbox result
                sandbox_variables = sandbox_result.get("variables", {})
                
                # Create a properly formatted current_variables dictionary
                current_variables = {}
                # Create a copy of items to avoid modifying during iteration
                for var_name, var_info in dict(sandbox_variables).items():
                    if isinstance(var_info, dict) and "type" in var_info:
                        # This is already a formatted DataFrame or other structured data
                        current_variables[var_name] = var_info
                    else:
                        # For other types, create a simple descriptor
                        var_type = "unknown"
                        if isinstance(var_info, str):
                            var_type = "string"
                        elif isinstance(var_info, (int, float)):
                            var_type = type(var_info).__name__
                        elif var_info == "module":  # Common case for imported modules
                            var_type = "module"
                        else:
                            var_type = str(type(var_info))
                            
                        current_variables[var_name] = {
                            "type": var_type,
                            "description": f"{var_type}: {str(var_info)[:100]}" + ("..." if len(str(var_info)) > 100 else "")
                        }
                
                # Format the result
                print(f"[FORMATTED RESULT]: current_variables keys: {list(current_variables.keys())}")
                images = sandbox_result.get("plots", [])
                print("--------------------------------")
                print(f"[IMAGES tool]: {images}")
                print("--------------------------------")

                # Return a Command to update the state
                return Command(
                    update={
                        "output_image_paths": images,  # Update plots list
                        "current_variables": current_variables,  # Update variables
                        "messages": [
                            ToolMessage(
                                content=json.dumps({
                                    "stdout": sandbox_result.get("output", ""),
                                    "plots": images,  # Include plots in the tool message content
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )
            else:
                error_msg = "Unknown error"
                if isinstance(sandbox_result, dict):
                    error_msg = sandbox_result.get("error", "Unknown error")
                elif isinstance(sandbox_result, str):
                    error_msg = sandbox_result
                return Command(
                    update={
                        "output_image_paths": [],  # Clear plots on error
                        "current_variables": {},  # Clear variables on error
                        "messages": [
                            ToolMessage(
                                content=json.dumps({
                                    "stdout": f"Error: {error_msg}",
                                    "plots": [],  # Empty plots list on error
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )

        # Run the async function
        return asyncio.run(execute_in_sandbox())

    except Exception as e:
        error_msg = f"Error executing code: {str(e)}"
        return Command(
            update={
                "output_image_paths": [],  # Clear plots on error
                "current_variables": {},  # Clear variables on error
                "messages": [
                    ToolMessage(
                        content=json.dumps({
                            "stdout": error_msg,
                            "plots": [],  # Empty plots list on error

                        }),
                        tool_call_id=tool_call_id
                    )
                ]
            }
        )

from prompts import WRITE_TODOS_DESCRIPTION
from typing import Literal
from typing_extensions import TypedDict
class Todo(TypedDict):
    """Todo to track."""

    content: str
    status: Literal["pending", "in_progress", "completed"]

@tool(description=WRITE_TODOS_DESCRIPTION)
def write_todos(
    todos: list[Todo], tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """
    Update the agent state's todos and emit a JSON ToolMessage content to avoid UI JSON parsing errors.
    """
    return Command(
        update={
            "todos": todos,
            "messages": [
                ToolMessage(
                    content=json.dumps(
                        {
                            "status": "ok",
                            "action": "write_todos",
                            "todos": todos,
                        },
                        ensure_ascii=False,
                    ),
                    tool_call_id=tool_call_id,
                )
            ],
        }
    )
